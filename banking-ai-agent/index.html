<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能银行助手 - AI驱动的金融服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary); color: var(--text-primary);">
    <!-- AI Agent 界面容器 -->
    <div id="ai-agent-container" class="min-h-screen flex flex-col">
        
        <!-- 顶部导航栏 -->
        <nav class="backdrop-blur-xl sticky top-0 z-50" style="background-color: var(--bg-primary); border-bottom: 1px solid var(--border-primary);">
            <div class="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-sm">AI</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-semibold" style="color: var(--text-primary);">智能银行助手</h1>
                        <p class="text-xs" style="color: var(--text-tertiary);">AI驱动的智能金融服务</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="theme-toggle" class="p-2 rounded-lg transition-colors" style="background-color: var(--bg-tertiary); color: var(--text-primary);" onmouseover="this.style.backgroundColor='var(--bg-quaternary)'" onmouseout="this.style.backgroundColor='var(--bg-tertiary)'">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                    <!-- 核心支柱：逃生通道 - 无缝人工介入 -->
                    <button onclick="aiAgent.contactHumanAgent()"
                            class="px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 rounded-lg transition-all flex items-center space-x-2 shadow-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>联系专家</span>
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    </button>

                    <!-- 核心支柱：普惠关怀 - 无障碍设计 -->
                    <button onclick="aiAgent.toggleAccessibilityMode()"
                            class="p-2 rounded-lg transition-colors" style="background-color: var(--bg-tertiary); color: var(--text-primary);" onmouseover="this.style.backgroundColor='var(--bg-quaternary)'" onmouseout="this.style.backgroundColor='var(--bg-tertiary)'"
                            title="无障碍模式">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="flex-1 max-w-7xl mx-auto w-full px-4 py-8" style="background-color: var(--bg-secondary);">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- 左侧：对话区域 -->
                <div class="lg:col-span-2 space-y-4">
                    <!-- 对话消息区域容器 -->
                    <div class="chat-container rounded-2xl relative" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <!-- 对话消息区域 -->
                        <div id="chat-messages" class="chat-messages-area">
                            <!-- 智能欢迎消息 -->
                            <div class="ai-message" id="welcome-message">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <span class="text-white text-sm font-bold">AI</span>
                                    </div>
                                    <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                                        <!-- 动态加载的个性化欢迎内容 -->
                                        <div id="personalized-greeting" style="color: var(--text-primary);">
                                            <!-- 将由JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 滚动到底部按钮 -->
                        <button id="scroll-to-bottom" class="scroll-to-bottom-btn hidden" onclick="aiAgent.scrollToBottom()" title="滚动到底部">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- AI 思维过程展示区（移动到对话区域底部） -->
                    <div id="ai-thinking" class="rounded-2xl p-6 hidden" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-75"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                            </div>
                            <span class="text-sm text-gray-400">AI 正在思考...</span>
                        </div>
                        <div id="thinking-content" class="space-y-2 text-sm text-gray-300">
                            <!-- 动态插入思维链内容 -->
                        </div>
                    </div>

                    <!-- 动态方案展示区 -->
                    <div id="solution-cards" class="hidden space-y-4">
                        <!-- 动态插入方案卡片 -->
                    </div>

                    <!-- 执行进度展示 -->
                    <div id="execution-progress" class="hidden rounded-2xl p-6" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <h3 class="text-lg font-semibold mb-4 flex items-center" style="color: var(--text-primary);">
                            <svg class="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            执行进度
                        </h3>
                        <div id="progress-steps" class="space-y-3">
                            <!-- 动态插入执行步骤 -->
                        </div>
                    </div>
                </div>

                <!-- 右侧：信息面板 -->
                <div class="lg:col-span-1 space-y-4">
                    <!-- 账户信息卡片 -->
                    <div class="bg-gray-900/50 rounded-2xl p-6 border border-gray-800 hover:border-gray-700 transition-all">
                        <h3 class="text-lg font-semibold mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            账户信息
                            <div class="ml-auto">
                                <div class="status-indicator online" title="实时同步"></div>
                            </div>
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">当前卡号</span>
                                <span class="font-mono text-blue-400">**** 8899</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">卡片类型</span>
                                <span class="badge success">工资卡</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">服务状态</span>
                                <span id="service-status" class="badge warning">未开通</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">月均交易</span>
                                <span class="text-blue-400 font-semibold">18笔</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-400">账户余额</span>
                                <span class="text-green-400 font-semibold">¥12,580.50</span>
                            </div>
                        </div>

                        <!-- 账户活跃度指示器 -->
                        <div class="mt-4 pt-4 border-t border-gray-700">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-400">账户活跃度</span>
                                <span class="text-sm text-green-400">活跃</span>
                            </div>
                            <div class="w-full bg-gray-800 rounded-full h-2">
                                <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full animate-pulse" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- AI 智能分析 -->
                    <div id="ai-insights" class="bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-2xl p-6 border border-blue-800/50 hover:border-blue-700/70 transition-all">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-blue-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold">AI 智能分析</h3>
                            </div>
                            <div class="badge primary">实时分析</div>
                        </div>

                        <div class="space-y-3">
                            <p class="text-sm text-gray-300 leading-relaxed">基于您的账户使用情况和交易模式，我为您分析了当前的金融服务状态，并提供个性化的优化建议。</p>

                            <!-- 智能建议列表 -->
                            <div class="space-y-2">
                                <div class="flex items-start space-x-2 text-xs">
                                    <span class="text-green-400">✓</span>
                                    <span class="text-gray-400">账户活跃度良好，适合开通增值服务</span>
                                </div>
                                <div class="flex items-start space-x-2 text-xs">
                                    <span class="text-blue-400">💡</span>
                                    <span class="text-gray-400">可考虑理财产品提升资金收益</span>
                                </div>
                                <div class="flex items-start space-x-2 text-xs">
                                    <span class="text-purple-400">📊</span>
                                    <span class="text-gray-400">建议开通智能通知服务</span>
                                </div>
                            </div>

                            <!-- 置信度指示器 -->
                            <div class="mt-4 pt-3 border-t border-blue-800/30">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-xs text-gray-400">分析置信度</span>
                                    <span class="text-xs text-blue-400 font-semibold">92%</span>
                                </div>
                                <div class="w-full bg-gray-800 rounded-full h-1.5">
                                    <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full" style="width: 92%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时通知面板 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 hover:border-gray-700 transition-all">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-sm flex items-center">
                                <svg class="w-4 h-4 mr-2 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 7l2.829 2.828A4 4 0 019.071 11H13l-4 8-4-8h3.071a4 4 0 01-2.414-1.172L2.828 7z"></path>
                                </svg>
                                实时通知
                            </h4>
                            <div class="status-indicator online"></div>
                        </div>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                                <span class="text-gray-400">今日通知</span>
                                <span class="text-blue-400 font-semibold">3条</span>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                                <span class="text-gray-400">本月费用</span>
                                <span class="text-green-400 font-semibold">¥2.00</span>
                            </div>
                            <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                                <span class="text-gray-400">节省通知</span>
                                <span class="text-purple-400 font-semibold">12条</span>
                            </div>
                        </div>
                    </div>

                    <!-- 安全提示 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 hover:border-green-800/50 transition-all">
                        <div class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <div>
                                <h4 class="font-semibold text-sm">安全保障</h4>
                                <p class="text-xs text-gray-400 mt-1">全程加密传输，支持随时撤销</p>
                                <div class="flex items-center mt-2 space-x-2">
                                    <div class="badge success">SSL加密</div>
                                    <div class="badge primary">实时监控</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速统计 -->
                    <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800">
                        <h4 class="font-semibold text-sm mb-3">服务统计</h4>
                        <div class="grid grid-cols-2 gap-3 text-center">
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="text-lg font-bold text-blue-400">2.3万</div>
                                <div class="text-xs text-gray-400">今日服务人次</div>
                            </div>
                            <div class="bg-gray-800/50 rounded-lg p-3">
                                <div class="text-lg font-bold text-green-400">98.5%</div>
                                <div class="text-xs text-gray-400">用户满意度</div>
                            </div>
                        </div>
                    </div>

                    <!-- 费用分析图表 -->
                    <div id="cost-analysis" class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800 hidden">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-sm">💰 费用分析</h4>
                            <button onclick="aiAgent.toggleCostAnalysis()" class="text-xs text-blue-400 hover:text-blue-300">
                                详细 →
                            </button>
                        </div>
                        <div class="space-y-3">
                            <!-- 费用对比条形图 -->
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-400">当前月费</span>
                                    <span class="text-red-400">¥6</span>
                                </div>
                                <div class="w-full bg-gray-800 rounded-full h-2">
                                    <div class="bg-red-400 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-400">优化后</span>
                                    <span class="text-green-400">¥2</span>
                                </div>
                                <div class="w-full bg-gray-800 rounded-full h-2">
                                    <div class="bg-green-400 h-2 rounded-full" style="width: 25%"></div>
                                </div>
                            </div>
                            <div class="text-center pt-2 border-t border-gray-700">
                                <div class="text-sm font-semibold text-green-400">年节省 ¥48</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部输入区域 -->
        <div class="backdrop-blur-xl sticky bottom-0" style="border-top: 1px solid var(--border-primary); background-color: var(--bg-primary);">
            <div class="max-w-7xl mx-auto px-4 py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-1 relative">
                        <input type="text" id="user-input" placeholder="告诉我您需要什么帮助，比如：我想转账、申请贷款、购买理财产品..."
                               class="w-full rounded-xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all" style="background-color: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-primary);">
                        <button id="voice-input" class="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-blue-400 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                            </svg>
                        </button>
                    </div>
                    <button onclick="sendMessage()" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-xl font-medium transition-all hover:scale-105 flex items-center space-x-2">
                        <span>发送</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </div>
                <div class="mt-2 text-xs text-gray-500 text-center">
                    按 Enter 发送消息 • 支持语音输入 • 智能银行助手24小时在线
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/app.js"></script>
    <script src="scripts/animations.js"></script>
</body>
</html>
