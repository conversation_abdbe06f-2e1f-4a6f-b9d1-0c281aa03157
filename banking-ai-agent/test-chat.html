<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天区域测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: var(--font-family);
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-4 text-center">聊天区域高度自适应测试</h1>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：测试聊天容器 -->
            <div class="lg:col-span-2">
                <div class="chat-container rounded-2xl relative mb-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
            <div id="chat-messages" class="chat-messages-area">
                <div class="ai-message">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-sm font-bold">AI</span>
                        </div>
                        <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                            <div style="color: var(--text-primary);">欢迎使用聊天区域测试！这是第一条消息。</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 滚动到底部按钮 -->
            <button id="scroll-to-bottom" class="scroll-to-bottom-btn hidden" onclick="scrollToBottom()" title="滚动到底部">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </button>
            </div>
            </div>

            <!-- 右侧：模拟信息面板 -->
            <div class="lg:col-span-1 space-y-4">
                <div class="bg-gray-900/50 rounded-2xl p-4 border border-gray-800">
                    <h3 class="text-lg font-semibold mb-4">模拟右侧面板</h3>
                    <div id="panel-content" class="space-y-3">
                        <div class="bg-gray-800/50 rounded-lg p-3">
                            <div class="text-sm text-gray-300">这是模拟的右侧面板内容</div>
                        </div>
                        <div class="bg-gray-800/50 rounded-lg p-3">
                            <div class="text-sm text-gray-300">聊天容器高度会自动调整</div>
                        </div>
                        <div class="bg-gray-800/50 rounded-lg p-3">
                            <div class="text-sm text-gray-300">与此面板的底边对齐</div>
                        </div>
                    </div>
                    <button onclick="addPanelContent()" class="mt-4 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                        增加面板内容
                    </button>
                    <button onclick="removePanelContent()" class="mt-4 ml-2 px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm">
                        减少面板内容
                    </button>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="flex flex-wrap gap-2 mb-4">
            <button onclick="addTestMessage('AI')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg">添加AI消息</button>
            <button onclick="addTestMessage('User')" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg">添加用户消息</button>
            <button onclick="testThinkingProcess()" class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg">测试思考过程</button>
            <button onclick="addMultipleMessages()" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg">添加多条消息</button>
            <button onclick="clearMessages()" class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg">清空消息</button>
            <button onclick="scrollToBottom()" class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg">滚动到底部</button>
        </div>

        <!-- 说明 -->
        <div class="bg-gray-800 rounded-lg p-4 text-sm">
            <h3 class="font-semibold mb-2">测试功能：</h3>
            <ul class="space-y-1 text-gray-300">
                <li>• <strong>思考过程集成</strong>：AI思考过程作为聊天消息流的一部分</li>
                <li>• <strong>可折叠思考</strong>：思考完成后自动折叠，可点击展开查看</li>
                <li>• <strong>自适应高度</strong>：聊天容器高度自动匹配右侧面板高度</li>
                <li>• <strong>动态调整</strong>：右侧面板内容变化时自动重新计算高度</li>
                <li>• <strong>响应式设计</strong>：桌面端自适应，移动端使用固定高度</li>
                <li>• <strong>自动滚动</strong>：新消息时自动滚动到底部</li>
                <li>• <strong>智能滚动</strong>：用户手动滚动时不会强制滚动到底部</li>
                <li>• <strong>滚动按钮</strong>：距离底部超过150px时显示</li>
                <li>• <strong>新消息提示</strong>：用户不在底部时显示红点提示</li>
                <li>• <strong>键盘快捷键</strong>：按End键滚动到底部</li>
                <li>• <strong>平滑滚动</strong>：使用CSS smooth滚动效果</li>
            </ul>
            <div class="mt-4 p-3 bg-blue-900/20 rounded-lg">
                <h4 class="font-semibold text-blue-400 mb-2">测试方法：</h4>
                <ul class="text-sm text-gray-300 space-y-1">
                    <li>1. 点击"测试思考过程"按钮，观察AI思考消息的显示和折叠</li>
                    <li>2. 点击"增加面板内容"按钮，观察聊天容器高度变化</li>
                    <li>3. 点击"减少面板内容"按钮，观察聊天容器高度调整</li>
                    <li>4. 调整浏览器窗口大小，测试响应式行为</li>
                    <li>5. 添加多条聊天消息，测试滚动功能</li>
                    <li>6. 点击折叠的思考过程，查看详细思考步骤</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 1;

        function addTestMessage(type, customContent = null) {
            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageCount++;

            if (type === 'AI') {
                messageDiv.className = 'ai-message';
                const content = customContent || `这是第${messageCount}条AI消息。测试聊天区域的滚动功能是否正常工作。`;
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-sm font-bold">AI</span>
                        </div>
                        <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                            <div style="color: var(--text-primary);">${content}</div>
                        </div>
                    </div>
                `;
            } else {
                messageDiv.className = 'user-message';
                const content = customContent || `这是第${messageCount}条用户消息。`;
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3 justify-end">
                        <div class="flex-1 rounded-2xl p-4 max-w-md" style="background: var(--gradient-primary);">
                            <p class="text-white">${content}</p>
                        </div>
                        <div class="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0" style="background-color: var(--bg-quaternary);">
                            <span class="text-sm" style="color: var(--text-primary);">您</span>
                        </div>
                    </div>
                `;
            }

            messagesDiv.appendChild(messageDiv);
            smartScrollToBottom();
        }

        function addMultipleMessages() {
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    addTestMessage(i % 2 === 0 ? 'AI' : 'User');
                }, i * 500);
            }
        }

        function clearMessages() {
            const messagesDiv = document.getElementById('chat-messages');
            messagesDiv.innerHTML = `
                <div class="ai-message">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-sm font-bold">AI</span>
                        </div>
                        <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                            <div style="color: var(--text-primary);">消息已清空，这是重新开始的第一条消息。</div>
                        </div>
                    </div>
                </div>
            `;
            messageCount = 1;
            scrollToBottom();
        }

        function scrollToBottom() {
            const messagesDiv = document.getElementById('chat-messages');
            const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
            
            if (!messagesDiv) return;

            requestAnimationFrame(() => {
                messagesDiv.scrollTo({
                    top: messagesDiv.scrollHeight,
                    behavior: 'smooth'
                });

                if (scrollToBottomBtn) {
                    scrollToBottomBtn.classList.add('hidden');
                    scrollToBottomBtn.classList.remove('has-new-message');
                }
            });
        }

        function shouldAutoScroll() {
            const messagesDiv = document.getElementById('chat-messages');
            if (!messagesDiv) return true;

            const { scrollTop, scrollHeight, clientHeight } = messagesDiv;
            const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
            
            return distanceFromBottom < 100;
        }

        function smartScrollToBottom() {
            if (!shouldAutoScroll()) {
                showNewMessageIndicator();
                return;
            }
            scrollToBottom();
        }

        function showNewMessageIndicator() {
            const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
            if (scrollToBottomBtn && !scrollToBottomBtn.classList.contains('hidden')) {
                scrollToBottomBtn.classList.add('has-new-message');
            }
        }

        function toggleScrollToBottomButton() {
            const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
            if (!scrollToBottomBtn) return;

            const messagesDiv = document.getElementById('chat-messages');
            if (!messagesDiv) return;

            const { scrollTop, scrollHeight, clientHeight } = messagesDiv;
            const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

            if (distanceFromBottom > 150) {
                scrollToBottomBtn.classList.remove('hidden');
            } else {
                scrollToBottomBtn.classList.add('hidden');
            }
        }

        // 初始化滚动监听
        function initScrollListener() {
            const messagesDiv = document.getElementById('chat-messages');
            if (!messagesDiv) return;

            let lastScrollCheck = 0;

            const throttledScrollCheck = () => {
                const now = Date.now();
                if (now - lastScrollCheck > 100) {
                    toggleScrollToBottomButton();
                    lastScrollCheck = now;
                }
            };

            messagesDiv.addEventListener('scroll', throttledScrollCheck);
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'End' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
                const activeElement = document.activeElement;
                if (activeElement?.tagName !== 'INPUT' && activeElement?.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    scrollToBottom();
                }
            }
        });

        // 添加/移除面板内容的函数
        function addPanelContent() {
            const panelContent = document.getElementById('panel-content');
            const newContent = document.createElement('div');
            newContent.className = 'bg-gray-800/50 rounded-lg p-3';
            newContent.innerHTML = `<div class="text-sm text-gray-300">新增的面板内容 ${Date.now()}</div>`;
            panelContent.appendChild(newContent);

            // 重新计算聊天容器高度
            setTimeout(calculateAndSetChatHeight, 100);
        }

        function removePanelContent() {
            const panelContent = document.getElementById('panel-content');
            const lastChild = panelContent.lastElementChild;
            if (lastChild && panelContent.children.length > 3) {
                lastChild.remove();
                // 重新计算聊天容器高度
                setTimeout(calculateAndSetChatHeight, 100);
            }
        }

        // 计算并设置聊天容器高度
        function calculateAndSetChatHeight() {
            const chatContainer = document.querySelector('.chat-container');
            const rightPanel = document.querySelector('.lg\\:col-span-1');

            if (!chatContainer || !rightPanel) return;

            // 检查是否为桌面端
            const isDesktop = window.innerWidth > 768;

            if (!isDesktop) return;

            // 获取右侧面板的高度
            const rightPanelHeight = rightPanel.offsetHeight;

            // 设置聊天容器的高度
            const minHeight = 500;
            const newHeight = Math.max(rightPanelHeight, minHeight);

            chatContainer.style.height = `${newHeight}px`;

            console.log(`聊天容器高度已调整为: ${newHeight}px (右侧面板高度: ${rightPanelHeight}px)`);
        }

        // 测试思考过程
        async function testThinkingProcess() {
            // 显示思考消息
            showThinking();

            // 模拟思考步骤
            const steps = [
                { text: '分析用户需求', confidence: 98 },
                { text: '检索相关信息', confidence: 95 },
                { text: '生成解决方案', confidence: 92 },
                { text: '优化回复内容', confidence: 96 }
            ];

            await displayThinkingProcess(steps);

            // 隐藏思考过程并添加AI回复
            hideThinking();

            setTimeout(() => {
                addTestMessage('AI', '基于刚才的思考过程，我为您提供了最佳解决方案。');
            }, 500);
        }

        // 显示思考消息
        function showThinking() {
            const messagesDiv = document.getElementById('chat-messages');
            if (!messagesDiv) return;

            const thinkingMessageDiv = document.createElement('div');
            thinkingMessageDiv.id = 'ai-thinking-message';
            thinkingMessageDiv.className = 'ai-message thinking-message';
            thinkingMessageDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span class="text-white text-sm font-bold">AI</span>
                    </div>
                    <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-75"></div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                            </div>
                            <span class="text-sm text-gray-400">AI 正在思考...</span>
                        </div>
                        <div id="thinking-content" class="space-y-2 text-sm text-gray-300">
                            <!-- 动态插入思维链内容 -->
                        </div>
                    </div>
                </div>
            `;

            messagesDiv.appendChild(thinkingMessageDiv);
            smartScrollToBottom();
        }

        // 显示思考步骤
        async function displayThinkingProcess(steps) {
            const thinkingContent = document.getElementById('thinking-content');
            if (!thinkingContent) return;

            for (let i = 0; i < steps.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 800));

                const step = steps[i];
                const stepElement = document.createElement('div');
                stepElement.className = 'thinking-step bg-gray-800/50 rounded-lg p-3 border-l-3 border-blue-500';
                stepElement.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-blue-400">🔍</span>
                            <span class="text-sm text-gray-300">${step.text}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="text-xs text-gray-500">${step.confidence}%</div>
                            <div class="w-4 h-4 text-green-400">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                `;

                thinkingContent.appendChild(stepElement);
                smartScrollToBottom();
            }
        }

        // 隐藏思考过程
        function hideThinking() {
            const thinkingMessageDiv = document.getElementById('ai-thinking-message');
            if (thinkingMessageDiv) {
                convertThinkingToCollapsible(thinkingMessageDiv);
                smartScrollToBottom();
            }
        }

        // 转换为可折叠形式
        function convertThinkingToCollapsible(thinkingMessageDiv) {
            const thinkingContent = thinkingMessageDiv.querySelector('#thinking-content');
            const stepsCount = thinkingContent ? thinkingContent.children.length : 0;

            const collapsedContent = `
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span class="text-white text-sm font-bold">AI</span>
                    </div>
                    <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <div class="thinking-summary cursor-pointer" onclick="toggleThinkingDetails('${thinkingMessageDiv.id}')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-400">💭 思考过程</span>
                                    <span class="text-xs text-gray-500">(${stepsCount} 个步骤)</span>
                                </div>
                                <svg class="w-4 h-4 text-gray-400 transition-transform duration-200" id="thinking-toggle-${thinkingMessageDiv.id}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div id="thinking-details-${thinkingMessageDiv.id}" class="thinking-details hidden mt-3">
                            ${thinkingContent ? thinkingContent.outerHTML : ''}
                        </div>
                    </div>
                </div>
            `;

            thinkingMessageDiv.innerHTML = collapsedContent;
            thinkingMessageDiv.classList.add('thinking-collapsed');
        }

        // 切换思考详情
        function toggleThinkingDetails(messageId) {
            const detailsDiv = document.getElementById(`thinking-details-${messageId}`);
            const toggleIcon = document.getElementById(`thinking-toggle-${messageId}`);

            if (detailsDiv && toggleIcon) {
                if (detailsDiv.classList.contains('hidden')) {
                    detailsDiv.classList.remove('hidden');
                    toggleIcon.style.transform = 'rotate(180deg)';
                } else {
                    detailsDiv.classList.add('hidden');
                    toggleIcon.style.transform = 'rotate(0deg)';
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initScrollListener();

            // 初始化高度调整
            setTimeout(calculateAndSetChatHeight, 100);

            // 监听窗口大小变化
            window.addEventListener('resize', calculateAndSetChatHeight);
        });
    </script>
</body>
</html>
