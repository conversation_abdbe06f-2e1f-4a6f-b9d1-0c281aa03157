<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天区域测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: var(--font-family);
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-4 text-center">聊天区域滚动功能测试</h1>
        
        <!-- 测试聊天容器 -->
        <div class="chat-container rounded-2xl relative mb-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
            <div id="chat-messages" class="chat-messages-area">
                <div class="ai-message">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-sm font-bold">AI</span>
                        </div>
                        <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                            <div style="color: var(--text-primary);">欢迎使用聊天区域测试！这是第一条消息。</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 滚动到底部按钮 -->
            <button id="scroll-to-bottom" class="scroll-to-bottom-btn hidden" onclick="scrollToBottom()" title="滚动到底部">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </button>
        </div>

        <!-- 测试按钮 -->
        <div class="flex flex-wrap gap-2 mb-4">
            <button onclick="addTestMessage('AI')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg">添加AI消息</button>
            <button onclick="addTestMessage('User')" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg">添加用户消息</button>
            <button onclick="addMultipleMessages()" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg">添加多条消息</button>
            <button onclick="clearMessages()" class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg">清空消息</button>
            <button onclick="scrollToBottom()" class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg">滚动到底部</button>
        </div>

        <!-- 说明 -->
        <div class="bg-gray-800 rounded-lg p-4 text-sm">
            <h3 class="font-semibold mb-2">测试功能：</h3>
            <ul class="space-y-1 text-gray-300">
                <li>• 固定容器高度：600px（移动端自适应）</li>
                <li>• 自动滚动：新消息时自动滚动到底部</li>
                <li>• 智能滚动：用户手动滚动时不会强制滚动到底部</li>
                <li>• 滚动按钮：距离底部超过150px时显示</li>
                <li>• 新消息提示：用户不在底部时显示红点提示</li>
                <li>• 键盘快捷键：按End键滚动到底部</li>
                <li>• 平滑滚动：使用CSS smooth滚动效果</li>
            </ul>
        </div>
    </div>

    <script>
        let messageCount = 1;

        function addTestMessage(type) {
            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageCount++;

            if (type === 'AI') {
                messageDiv.className = 'ai-message';
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-sm font-bold">AI</span>
                        </div>
                        <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                            <div style="color: var(--text-primary);">这是第${messageCount}条AI消息。测试聊天区域的滚动功能是否正常工作。</div>
                        </div>
                    </div>
                `;
            } else {
                messageDiv.className = 'user-message';
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3 justify-end">
                        <div class="flex-1 rounded-2xl p-4 max-w-md" style="background: var(--gradient-primary);">
                            <p class="text-white">这是第${messageCount}条用户消息。</p>
                        </div>
                        <div class="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0" style="background-color: var(--bg-quaternary);">
                            <span class="text-sm" style="color: var(--text-primary);">您</span>
                        </div>
                    </div>
                `;
            }

            messagesDiv.appendChild(messageDiv);
            smartScrollToBottom();
        }

        function addMultipleMessages() {
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    addTestMessage(i % 2 === 0 ? 'AI' : 'User');
                }, i * 500);
            }
        }

        function clearMessages() {
            const messagesDiv = document.getElementById('chat-messages');
            messagesDiv.innerHTML = `
                <div class="ai-message">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-white text-sm font-bold">AI</span>
                        </div>
                        <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                            <div style="color: var(--text-primary);">消息已清空，这是重新开始的第一条消息。</div>
                        </div>
                    </div>
                </div>
            `;
            messageCount = 1;
            scrollToBottom();
        }

        function scrollToBottom() {
            const messagesDiv = document.getElementById('chat-messages');
            const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
            
            if (!messagesDiv) return;

            requestAnimationFrame(() => {
                messagesDiv.scrollTo({
                    top: messagesDiv.scrollHeight,
                    behavior: 'smooth'
                });

                if (scrollToBottomBtn) {
                    scrollToBottomBtn.classList.add('hidden');
                    scrollToBottomBtn.classList.remove('has-new-message');
                }
            });
        }

        function shouldAutoScroll() {
            const messagesDiv = document.getElementById('chat-messages');
            if (!messagesDiv) return true;

            const { scrollTop, scrollHeight, clientHeight } = messagesDiv;
            const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
            
            return distanceFromBottom < 100;
        }

        function smartScrollToBottom() {
            if (!shouldAutoScroll()) {
                showNewMessageIndicator();
                return;
            }
            scrollToBottom();
        }

        function showNewMessageIndicator() {
            const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
            if (scrollToBottomBtn && !scrollToBottomBtn.classList.contains('hidden')) {
                scrollToBottomBtn.classList.add('has-new-message');
            }
        }

        function toggleScrollToBottomButton() {
            const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
            if (!scrollToBottomBtn) return;

            const messagesDiv = document.getElementById('chat-messages');
            if (!messagesDiv) return;

            const { scrollTop, scrollHeight, clientHeight } = messagesDiv;
            const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

            if (distanceFromBottom > 150) {
                scrollToBottomBtn.classList.remove('hidden');
            } else {
                scrollToBottomBtn.classList.add('hidden');
            }
        }

        // 初始化滚动监听
        function initScrollListener() {
            const messagesDiv = document.getElementById('chat-messages');
            if (!messagesDiv) return;

            let lastScrollCheck = 0;

            const throttledScrollCheck = () => {
                const now = Date.now();
                if (now - lastScrollCheck > 100) {
                    toggleScrollToBottomButton();
                    lastScrollCheck = now;
                }
            };

            messagesDiv.addEventListener('scroll', throttledScrollCheck);
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'End' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
                const activeElement = document.activeElement;
                if (activeElement?.tagName !== 'INPUT' && activeElement?.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    scrollToBottom();
                }
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initScrollListener();
        });
    </script>
</body>
</html>
