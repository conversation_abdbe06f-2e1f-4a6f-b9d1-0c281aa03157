// ===== 银信通AI Agent 核心应用 =====

// 全局状态管理
class AppState {
    constructor() {
        this.currentTheme = 'dark';
        this.isVoiceEnabled = false;
        this.currentService = null;
        this.conversationHistory = [];
        this.sessionData = {
            startTime: new Date(),
            interactions: 0,
            completedServices: []
        };
        this.userData = {
            cards: [
                {
                    number: '8899',
                    type: '工资卡',
                    hasService: false,
                    monthlyTransactions: 18,
                    balance: 12580.50,
                    lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2天前
                },
                {
                    number: '6789',
                    type: '储蓄卡',
                    hasService: true,
                    lastTransaction: '3个月前',
                    monthlyFee: 2,
                    balance: 3200.00,
                    lastActivity: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // 90天前
                },
                {
                    number: '1234',
                    type: '信用卡',
                    hasService: true,
                    monthlyTransactions: 25,
                    balance: -1580.30,
                    lastActivity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1天前
                }
            ],
            preferences: {
                notificationThreshold: 50,
                preferredChannel: 'SMS',
                language: 'zh-CN',
                timezone: 'Asia/Shanghai'
            },
            profile: {
                riskLevel: 'low',
                customerType: 'individual',
                joinDate: new Date('2020-03-15'),
                lastLogin: new Date()
            }
        };
        this.businessState = {
            currentFlow: null,
            currentStep: 0,
            totalSteps: 0,
            flowData: {},
            errors: [],
            warnings: []
        };
    }

    getCurrentCard() {
        return this.userData.cards[0]; // 默认返回第一张卡
    }

    updateCardService(cardNumber, hasService) {
        const card = this.userData.cards.find(c => c.number === cardNumber);
        if (card) {
            card.hasService = hasService;
            this.logStateChange('card_service_updated', { cardNumber, hasService });
        }
    }

    addToHistory(type, data) {
        this.conversationHistory.push({
            timestamp: new Date(),
            type,
            data,
            sessionId: this.getSessionId()
        });

        // 限制历史记录长度
        if (this.conversationHistory.length > 100) {
            this.conversationHistory = this.conversationHistory.slice(-50);
        }
    }

    getSessionId() {
        if (!this.sessionId) {
            this.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        return this.sessionId;
    }

    updateBusinessState(updates) {
        Object.assign(this.businessState, updates);
        this.logStateChange('business_state_updated', updates);
    }

    logStateChange(action, data) {
        console.log(`[StateManager] ${action}:`, data);
        this.addToHistory('state_change', { action, data });
    }

    getAnalytics() {
        return {
            sessionDuration: Date.now() - this.sessionData.startTime.getTime(),
            interactions: this.sessionData.interactions,
            completedServices: this.sessionData.completedServices.length,
            conversationLength: this.conversationHistory.length,
            activeCards: this.userData.cards.filter(card => card.hasService).length,
            totalBalance: this.userData.cards.reduce((sum, card) => sum + card.balance, 0)
        };
    }

    incrementInteraction() {
        this.sessionData.interactions++;
    }

    addCompletedService(serviceType, details) {
        this.sessionData.completedServices.push({
            type: serviceType,
            timestamp: new Date(),
            details
        });
    }

    // 智能推荐引擎
    getRecommendations() {
        const recommendations = [];
        const currentCard = this.getCurrentCard();

        // 基于账户活跃度推荐
        if (currentCard.monthlyTransactions > 15 && !currentCard.hasService) {
            recommendations.push({
                type: 'service_activation',
                priority: 'high',
                reason: '账户交易频繁，建议开通通知服务',
                confidence: 0.95
            });
        }

        // 基于费用优化推荐
        const inactiveCards = this.userData.cards.filter(card =>
            card.hasService && card.lastActivity < new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
        );

        if (inactiveCards.length > 0) {
            recommendations.push({
                type: 'cost_optimization',
                priority: 'medium',
                reason: `${inactiveCards.length}张卡长期未使用但仍在扣费`,
                confidence: 0.88,
                savings: inactiveCards.length * 24 // 年节省金额
            });
        }

        return recommendations;
    }

    // 风险评估
    assessRisk(serviceType, cardNumber) {
        const card = this.userData.cards.find(c => c.number === cardNumber);
        if (!card) return { level: 'high', reasons: ['卡片不存在'] };

        const risks = [];
        let level = 'low';

        // 检查账户余额
        if (card.balance < 100) {
            risks.push('账户余额较低');
            level = 'medium';
        }

        // 检查交易频率
        if (card.monthlyTransactions < 5) {
            risks.push('账户使用频率较低');
            if (level === 'low') level = 'medium';
        }

        // 检查服务重复
        if (serviceType === 'subscribe' && card.hasService) {
            risks.push('该卡已开通服务');
            level = 'high';
        }

        return { level, reasons: risks };
    }
}

// AI Agent 核心类
class BankingAIAgent {
    constructor() {
        this.state = new AppState();
        this.isThinking = false;
        this.currentStep = 0;
        this.thinkingSteps = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeTheme();
        this.showWelcomeMessage();
        this.initializeTooltips();
        this.startRealtimeUpdates();
        this.initializeChatScrollListener();
        this.adjustChatContainerHeight();
    }

    setupEventListeners() {
        // 主题切换
        document.getElementById('theme-toggle')?.addEventListener('click', () => {
            this.toggleTheme();
        });

        // 回车发送消息
        document.getElementById('user-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            // End键滚动到底部
            if (e.key === 'End' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
                const activeElement = document.activeElement;
                // 只有当焦点不在输入框时才响应End键
                if (activeElement?.tagName !== 'INPUT' && activeElement?.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    this.scrollToBottom();
                }
            }
        });

        // 语音输入
        document.getElementById('voice-input')?.addEventListener('click', () => {
            this.toggleVoiceInput();
        });

        // 智能输入验证
        this.setupSmartInputValidation();

        // 键盘快捷键
        this.setupKeyboardShortcuts();

        // 无障碍支持
        this.setupAccessibility();

        // 演示模式
        this.setupDemoMode();

        // 性能监控
        this.setupPerformanceMonitoring();
    }

    initializeTheme() {
        const savedTheme = localStorage.getItem('theme') || 'dark';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const newTheme = this.state.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        this.state.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // 更新主题切换按钮图标
        const themeButton = document.getElementById('theme-toggle');
        if (themeButton) {
            const icon = themeButton.querySelector('svg path');
            if (theme === 'light') {
                icon.setAttribute('d', 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z');
            } else {
                icon.setAttribute('d', 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z');
            }
        }
    }

    showWelcomeMessage() {
        // 实现智能感知与主动服务
        this.initializeIntelligentEngagement();
        this.addAIInsight();
    }

    // 第一阶段：智能感知与主动服务
    async initializeIntelligentEngagement() {
        // 1. 主动问候与身份识别
        await this.performProactiveGreeting();

        // 2. 情境感知
        await this.performContextualAwareness();

        // 3. 个性化服务准备
        this.preparePersonalizedService();
    }

    async performProactiveGreeting() {
        const greetingContainer = document.getElementById('personalized-greeting');
        if (!greetingContainer) return;

        // 模拟身份识别过程
        greetingContainer.innerHTML = `
            <div class="flex items-center space-x-2 mb-3">
                <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-blue-400">正在识别您的身份...</span>
            </div>
        `;

        await this.delay(1500);

        // 模拟用户识别结果
        const userProfile = this.getUserProfile();
        const currentTime = new Date();
        const timeGreeting = this.getTimeBasedGreeting(currentTime);

        greetingContainer.innerHTML = `
            <div class="space-y-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✓</span>
                    <span class="text-sm text-green-400">身份验证成功</span>
                </div>

                <div>
                    <p class="mb-2" style="color: var(--text-primary);">
                        ${timeGreeting}，${userProfile.name}！我是您的专属AI银行助手小慧。
                    </p>
                    <p class="text-sm mb-3" style="color: var(--text-secondary);">
                        检测到您是${userProfile.customerType}，上次访问时间：${userProfile.lastVisit}
                    </p>
                </div>

                <div class="rounded-lg p-3" style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3);">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-blue-400">🎯</span>
                        <span class="text-sm font-medium text-blue-400">智能推荐</span>
                    </div>
                    <p class="text-xs mb-2" style="color: var(--text-secondary);">基于您的账户活动，我为您准备了以下服务：</p>
                    <div id="intelligent-recommendations" class="space-y-2">
                        <!-- 动态生成的智能推荐 -->
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="aiAgent.startPersonalizedService()"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-sm text-white">
                        开始个性化服务
                    </button>
                </div>
            </div>
        `;

        // 生成智能推荐
        await this.delay(500);
        this.generateIntelligentRecommendations();
    }

    getUserProfile() {
        // 模拟用户档案数据
        return {
            name: '张先生',
            customerType: '金卡客户',
            lastVisit: '2024年1月25日',
            accountType: '工资卡',
            riskLevel: 'low',
            preferredServices: ['银信通', '理财', '转账'],
            behaviorPattern: 'active'
        };
    }

    getTimeBasedGreeting(time) {
        const hour = time.getHours();
        if (hour < 6) return '夜深了';
        if (hour < 9) return '早上好';
        if (hour < 12) return '上午好';
        if (hour < 14) return '中午好';
        if (hour < 18) return '下午好';
        if (hour < 22) return '晚上好';
        return '夜深了';
    }

    async generateIntelligentRecommendations() {
        const recommendationsContainer = document.getElementById('intelligent-recommendations');
        if (!recommendationsContainer) return;

        const recommendations = [
            {
                icon: '📱',
                title: '银信通签约',
                description: '开通银信通短信提醒服务，实时掌握账户动态',
                confidence: 95,
                reason: '基于您的交易频率分析',
                serviceType: 'yxt_signup'
            },
            {
                icon: '✏️',
                title: '银信通修改',
                description: '修改银信通服务设置，优化提醒偏好',
                confidence: 85,
                reason: '基于您的使用习惯评估',
                serviceType: 'yxt_modify'
            },
            {
                icon: '❌',
                title: '银信通解约',
                description: '取消银信通服务，停止短信提醒',
                confidence: 75,
                reason: '基于服务使用情况分析',
                serviceType: 'yxt_cancel'
            }
        ];

        for (let i = 0; i < recommendations.length; i++) {
            await this.delay(300);
            const rec = recommendations[i];

            const recElement = document.createElement('div');
            recElement.className = 'flex items-center justify-between p-2 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer';
            recElement.innerHTML = `
                <div class="flex items-center space-x-2">
                    <span class="text-lg">${rec.icon}</span>
                    <div>
                        <div class="text-sm font-medium text-gray-200">${rec.title}</div>
                        <div class="text-xs text-gray-400">${rec.description}</div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-xs text-blue-400">${rec.confidence}%</div>
                    <div class="text-xs text-gray-500">置信度</div>
                </div>
            `;

            recElement.addEventListener('click', () => {
                this.handleRecommendationClick(rec);
            });

            recommendationsContainer.appendChild(recElement);
        }
    }

    async performContextualAwareness() {
        // 情境感知：分析用户当前状态
        const context = {
            timeOfDay: new Date().getHours(),
            deviceType: this.detectDeviceType(),
            networkQuality: 'good',
            userMood: this.detectUserMood(),
            urgencyLevel: 'normal'
        };

        // 根据情境调整交互方式
        this.adjustInteractionStyle(context);
    }

    detectDeviceType() {
        const userAgent = navigator.userAgent;
        if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
            return 'mobile';
        }
        return 'desktop';
    }

    detectUserMood() {
        // 模拟情绪检测（实际可通过语音语调、输入速度等分析）
        const moods = ['calm', 'urgent', 'confused', 'satisfied'];
        return moods[Math.floor(Math.random() * moods.length)];
    }

    adjustInteractionStyle(context) {
        // 根据情境调整交互风格
        if (context.timeOfDay < 9 || context.timeOfDay > 21) {
            // 早晚时间，使用更温和的语调
            this.interactionStyle = 'gentle';
        } else if (context.userMood === 'urgent') {
            // 用户着急，提供快速通道
            this.interactionStyle = 'efficient';
        } else {
            this.interactionStyle = 'standard';
        }
    }

    preparePersonalizedService() {
        // 准备个性化服务
        this.personalizedContext = {
            userProfile: this.getUserProfile(),
            recommendations: this.getPersonalizedRecommendations(),
            quickActions: this.getQuickActions(),
            riskProfile: this.getRiskProfile()
        };
    }

    startPersonalizedService() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-green-400">🚀</span>
                    <span class="font-semibold text-green-400">个性化服务已启动</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">
                    基于您的档案和偏好，我已为您准备了专属的服务体验。请告诉我您今天需要什么帮助？
                </p>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <button onclick="aiAgent.handleSmsServiceRequest('sms_notification', '银信通服务')"
                            class="p-2 bg-blue-600/20 rounded hover:bg-blue-600/30 transition-colors text-left">
                        <div class="font-medium text-blue-400">📱 银信通服务</div>
                        <div class="text-gray-400">推荐度: 95%</div>
                    </button>
                    <button onclick="aiAgent.handleWealthManagementRequest('理财咨询')"
                            class="p-2 bg-green-600/20 rounded hover:bg-green-600/30 transition-colors text-left">
                        <div class="font-medium text-green-400">💰 理财咨询</div>
                        <div class="text-gray-400">推荐度: 88%</div>
                    </button>
                    <button onclick="aiAgent.handleTransferRequest('转账汇款')"
                            class="p-2 bg-purple-600/20 rounded hover:bg-purple-600/30 transition-colors text-left">
                        <div class="font-medium text-purple-400">💸 转账汇款</div>
                        <div class="text-gray-400">常用服务</div>
                    </button>
                    <button onclick="aiAgent.handleAccountManagementRequest('账户管理')"
                            class="p-2 bg-cyan-600/20 rounded hover:bg-cyan-600/30 transition-colors text-left">
                        <div class="font-medium text-cyan-400">👤 账户管理</div>
                        <div class="text-gray-400">安全设置</div>
                    </button>
                </div>
            </div>
        `);
    }

    handleRecommendationClick(recommendation) {
        this.addAIMessage(`您选择了"${recommendation.title}"服务。${recommendation.reason}，置信度${recommendation.confidence}%。`);

        // 根据推荐类型启动相应服务
        switch (recommendation.title) {
            case '银信通签约':
                this.handleSmsServiceRequest('yxt_signup', '银信通签约');
                break;
            case '银信通修改':
                this.handleSmsServiceRequest('yxt_modify', '银信通修改');
                break;
            case '银信通解约':
                this.handleSmsServiceRequest('yxt_cancel', '银信通解约');
                break;
        }
    }

    handleSecurityUpgrade() {
        this.addAIMessage(`
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-yellow-400 mb-3">🔒 账户安全升级</h4>
                <p class="text-sm text-gray-300 mb-4">为了保护您的账户安全，建议开启以下安全功能：</p>
                <div class="space-y-2">
                    <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                        <span class="text-sm">双重验证</span>
                        <button class="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 rounded text-xs">开启</button>
                    </div>
                    <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                        <span class="text-sm">登录提醒</span>
                        <button class="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 rounded text-xs">开启</button>
                    </div>
                    <div class="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                        <span class="text-sm">异常监控</span>
                        <button class="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 rounded text-xs">开启</button>
                    </div>
                </div>
            </div>
        `);
    }

    addAIInsight() {
        const insightElement = document.getElementById('ai-insights');
        if (insightElement) {
            insightElement.classList.add('animate-fade-in-up');
        }
    }

    performInitialAnalysis() {
        // 只在用户主动触发时执行分析
        const recommendations = this.state.getRecommendations();
        const currentCard = this.state.getCurrentCard();

        // 更新AI洞察内容（不自动显示分析）
        this.updateAIInsights(currentCard, recommendations);
    }

    // 移除自动显示的通用分析，改为按需触发
    showGeneralAnalysis() {
        // 只在用户明确请求时显示分析
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-blue-400">🤖</span>
                    <span class="font-semibold text-blue-400">AI智能分析</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">基于您的账户情况，我为您推荐以下服务：</p>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <div class="p-2 bg-gray-800/50 rounded">
                        <div class="text-green-400 font-medium">💰 理财建议</div>
                        <div class="text-gray-400">账户余额充足，可考虑理财</div>
                    </div>
                    <div class="p-2 bg-gray-800/50 rounded">
                        <div class="text-blue-400 font-medium">📱 智能通知</div>
                        <div class="text-gray-400">建议开通账户变动提醒</div>
                    </div>
                    <div class="p-2 bg-gray-800/50 rounded">
                        <div class="text-purple-400 font-medium">💳 信用服务</div>
                        <div class="text-gray-400">可申请信用卡提升额度</div>
                    </div>
                    <div class="p-2 bg-gray-800/50 rounded">
                        <div class="text-yellow-400 font-medium">🛡️ 安全保障</div>
                        <div class="text-gray-400">建议开通账户安全险</div>
                    </div>
                </div>
                <div class="mt-3 text-xs text-gray-400 text-center">
                    告诉我您感兴趣的服务，我会为您详细介绍
                </div>
            </div>
        `);
    }

    analyzeBatchOptimization() {
        const cards = this.state.userData.cards;
        const issues = [];
        let totalSavings = 0;

        // 检查重复签约
        const activeServices = cards.filter(card => card.hasService);
        if (activeServices.length > 1) {
            const inactiveServices = activeServices.filter(card =>
                card.lastActivity && (Date.now() - card.lastActivity.getTime()) > 60*24*60*60*1000
            );

            if (inactiveServices.length > 0) {
                issues.push({
                    type: 'duplicate_services',
                    title: '重复签约检测',
                    description: `发现${inactiveServices.length}张长期未使用的卡片仍在扣费`,
                    cards: inactiveServices,
                    savings: inactiveServices.length * 24,
                    priority: 'high'
                });
                totalSavings += inactiveServices.length * 24;
            }
        }

        // 检查通知设置优化
        const suboptimalSettings = activeServices.filter(card => {
            // 模拟检查通知设置是否合理
            return Math.random() > 0.7; // 30%的卡片设置不够优化
        });

        if (suboptimalSettings.length > 0) {
            issues.push({
                type: 'settings_optimization',
                title: '通知设置优化',
                description: `${suboptimalSettings.length}张卡片的通知设置可以进一步优化`,
                cards: suboptimalSettings,
                savings: suboptimalSettings.length * 12, // 减少无效通知的价值
                priority: 'medium'
            });
        }

        // 检查费用优化机会
        if (activeServices.length >= 2) {
            issues.push({
                type: 'fee_optimization',
                title: '费用优化机会',
                description: '多卡用户可享受批量优惠',
                savings: Math.floor(activeServices.length * 2 * 0.1 * 12), // 10%折扣
                priority: 'low'
            });
        }

        return {
            needsOptimization: issues.length > 0,
            issues,
            totalSavings,
            affectedCards: [...new Set(issues.flatMap(issue => issue.cards || []))]
        };
    }

    showBatchOptimizationSuggestion(optimization) {
        this.addAIMessage(`
            <div class="bg-gradient-to-br from-orange-900/20 to-red-900/20 border border-orange-600/30 rounded-xl p-6">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🔍</span>
                    </div>
                    <div>
                        <h4 class="text-xl font-bold text-orange-400">智能批量优化建议</h4>
                        <p class="text-sm text-gray-400">AI发现了${optimization.issues.length}个可优化项</p>
                    </div>
                </div>

                <div class="space-y-4">
                    ${optimization.issues.map(issue => `
                        <div class="bg-gray-800/50 rounded-lg p-4 border-l-4 ${
                            issue.priority === 'high' ? 'border-red-500' :
                            issue.priority === 'medium' ? 'border-yellow-500' : 'border-blue-500'
                        }">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="font-semibold ${
                                    issue.priority === 'high' ? 'text-red-400' :
                                    issue.priority === 'medium' ? 'text-yellow-400' : 'text-blue-400'
                                }">${issue.title}</h5>
                                <span class="badge ${
                                    issue.priority === 'high' ? 'error' :
                                    issue.priority === 'medium' ? 'warning' : 'primary'
                                }">${issue.priority === 'high' ? '高优先级' : issue.priority === 'medium' ? '中优先级' : '低优先级'}</span>
                            </div>
                            <p class="text-sm text-gray-300 mb-2">${issue.description}</p>
                            ${issue.savings ? `
                                <div class="text-sm text-green-400">💰 预计年节省: ${issue.savings}元</div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>

                <div class="mt-6 bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-semibold text-green-400">总优化效果</div>
                            <div class="text-sm text-gray-400">预计年节省总额</div>
                        </div>
                        <div class="text-2xl font-bold text-green-400">¥${optimization.totalSavings}</div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="aiAgent.dismissOptimization()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                        稍后处理
                    </button>
                    <button onclick="aiAgent.startBatchOptimization(${JSON.stringify(optimization).replace(/"/g, '&quot;')})"
                            class="px-6 py-2 bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors font-medium">
                        一键优化
                    </button>
                </div>
            </div>
        `);
    }

    dismissOptimization() {
        this.addAIMessage('好的，您可以随时告诉我需要优化服务设置。我会继续为您监控账户状况。');
    }

    startBatchOptimization(optimization) {
        this.addAIMessage('正在启动智能批量优化流程...');

        setTimeout(() => {
            this.showBatchOptimizationProcess(optimization);
        }, 1000);
    }

    async showBatchOptimizationProcess(optimization) {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-blue-400 mb-3">🔄 批量优化执行计划</h4>
                <div class="space-y-2 text-sm">
                    ${optimization.issues.map((issue, index) => `
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center">
                                <span class="text-xs">${index + 1}</span>
                            </div>
                            <span class="text-gray-300">${issue.title}</span>
                            <span class="text-green-400 text-xs ml-auto">+¥${issue.savings || 0}</span>
                        </div>
                    `).join('')}
                </div>
                <div class="mt-4 pt-3 border-t border-blue-800/30">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-400">预计执行时间:</span>
                        <span class="text-blue-400">2-3分钟</span>
                    </div>
                </div>
            </div>
        `);

        // 模拟批量优化执行
        for (let i = 0; i < optimization.issues.length; i++) {
            await this.delay(1500);
            const issue = optimization.issues[i];

            this.addAIMessage(`
                <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">✅</span>
                        <span class="text-green-400 font-medium">${issue.title} 已完成</span>
                    </div>
                    ${issue.savings ? `
                        <div class="text-xs text-gray-400 mt-1">节省: ¥${issue.savings}/年</div>
                    ` : ''}
                </div>
            `);
        }

        // 移除自动跳转到最终结果
        this.addAIMessage(`
            <div class="text-center mt-4">
                <button onclick="aiAgent.showBatchOptimizationResults(${JSON.stringify(optimization).replace(/"/g, '&quot;')})"
                        class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                    查看优化结果
                </button>
            </div>
        `);
    }

    showBatchOptimizationResults(optimization) {
        this.addAIMessage(`
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
                    <span class="text-3xl">🎯</span>
                </div>

                <h3 class="text-2xl font-bold text-green-400 mb-2">批量优化完成！</h3>
                <p class="text-gray-300 mb-6">您的银信通服务已全面优化</p>

                <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 mb-6 border border-gray-700">
                    <h4 class="font-semibold text-gray-200 mb-4">📊 优化成果</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-400">${optimization.issues.length}</div>
                            <div class="text-sm text-gray-400">优化项目</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-400">¥${optimization.totalSavings}</div>
                            <div class="text-sm text-gray-400">年节省金额</div>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 mb-4">
                    <h5 class="font-semibold text-blue-400 mb-2">🤖 AI优化报告</h5>
                    <div class="text-sm text-gray-300 space-y-1">
                        <p>• 清理了${optimization.issues.filter(i => i.type === 'duplicate_services').length}个重复服务</p>
                        <p>• 优化了${optimization.issues.filter(i => i.type === 'settings_optimization').length}个通知设置</p>
                        <p>• 应用了智能费用优化策略</p>
                        <p>• 预计每月减少${Math.round(optimization.totalSavings/12)}元支出</p>
                    </div>
                </div>

                <button onclick="aiAgent.startNewService()"
                        class="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-all font-medium">
                    继续使用AI助手
                </button>
            </div>
        `);

        // 更新状态
        this.state.addCompletedService('batch_optimization', {
            issues: optimization.issues,
            totalSavings: optimization.totalSavings,
            timestamp: new Date()
        });
    }

    showProactiveRecommendation(recommendation) {
        const proactiveMessage = this.generateProactiveMessage(recommendation);
        this.addAIMessage(proactiveMessage, () => {
            if (recommendation.type === 'service_activation') {
                this.addAIMessage('我可以为您快速办理，整个过程只需2-3分钟。是否现在开始？');
            }
        });
    }

    generateProactiveMessage(recommendation) {
        const messages = {
            'service_activation': `
                <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-blue-400">💡</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-400 mb-1">智能建议</h4>
                            <p class="text-sm text-gray-300">${recommendation.reason}</p>
                            <div class="mt-2 text-xs text-blue-400">
                                AI置信度: ${Math.round(recommendation.confidence * 100)}%
                            </div>
                        </div>
                    </div>
                </div>
            `,
            'cost_optimization': `
                <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-yellow-400">💰</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-yellow-400 mb-1">费用优化建议</h4>
                            <p class="text-sm text-gray-300">${recommendation.reason}</p>
                            <div class="mt-2 text-xs text-green-400">
                                预计年节省: ¥${recommendation.savings}
                            </div>
                        </div>
                    </div>
                </div>
            `
        };

        return messages[recommendation.type] || recommendation.reason;
    }

    updateAIInsights(currentCard, recommendations) {
        // 动态更新AI洞察面板的内容
        const insightElement = document.getElementById('ai-insights');
        if (!insightElement) return;

        // 更新置信度显示（通用分析）
        const confidenceBar = insightElement.querySelector('.bg-gradient-to-r');
        const confidenceText = insightElement.querySelector('.text-blue-400.font-semibold');

        if (confidenceBar && confidenceText) {
            // 基于账户活跃度计算置信度
            const activityScore = currentCard.monthlyTransactions > 10 ? 92 : 78;
            confidenceBar.style.width = activityScore + '%';
            confidenceText.textContent = activityScore + '%';
        }
    }

    // 智能决策引擎
    makeIntelligentDecision(context) {
        const { userInput, conversationHistory, currentState } = context;

        // 分析用户意图强度
        const intentStrength = this.calculateIntentStrength(userInput);

        // 分析对话上下文
        const contextScore = this.analyzeConversationContext(conversationHistory);

        // 风险评估
        const riskAssessment = this.state.assessRisk(currentState.service, currentState.cardNumber);

        // 综合决策
        const decision = {
            confidence: (intentStrength + contextScore) / 2,
            riskLevel: riskAssessment.level,
            recommendedAction: this.determineRecommendedAction(intentStrength, contextScore, riskAssessment),
            reasoning: this.generateReasoning(intentStrength, contextScore, riskAssessment)
        };

        return decision;
    }

    calculateIntentStrength(userInput) {
        // 计算用户意图的强度
        const strongIndicators = ['一定要', '必须', '立即', '马上', '现在'];
        const weakIndicators = ['可能', '也许', '考虑', '看看'];

        let strength = 0.5; // 基础强度

        strongIndicators.forEach(indicator => {
            if (userInput.includes(indicator)) strength += 0.2;
        });

        weakIndicators.forEach(indicator => {
            if (userInput.includes(indicator)) strength -= 0.1;
        });

        return Math.max(0, Math.min(1, strength));
    }

    analyzeConversationContext(history) {
        // 分析对话上下文的相关性
        if (!history || history.length === 0) return 0.5;

        const recentMessages = history.slice(-5); // 最近5条消息
        let contextScore = 0.5;

        // 检查对话连贯性
        const topicConsistency = this.checkTopicConsistency(recentMessages);
        contextScore += topicConsistency * 0.3;

        // 检查用户参与度
        const engagementLevel = this.checkEngagementLevel(recentMessages);
        contextScore += engagementLevel * 0.2;

        return Math.max(0, Math.min(1, contextScore));
    }

    checkTopicConsistency(messages) {
        // 检查话题一致性
        const bankingKeywords = ['银信通', '通知', '短信', '账户', '卡片', '服务'];
        let relevantMessages = 0;

        messages.forEach(msg => {
            if (bankingKeywords.some(keyword => msg.data?.includes?.(keyword))) {
                relevantMessages++;
            }
        });

        return messages.length > 0 ? relevantMessages / messages.length : 0;
    }

    checkEngagementLevel(messages) {
        // 检查用户参与度
        const userMessages = messages.filter(msg => msg.type === 'user_message');
        const avgLength = userMessages.reduce((sum, msg) => sum + (msg.data?.length || 0), 0) / userMessages.length;

        // 根据消息长度判断参与度
        if (avgLength > 20) return 0.8;
        if (avgLength > 10) return 0.6;
        if (avgLength > 5) return 0.4;
        return 0.2;
    }

    determineRecommendedAction(intentStrength, contextScore, riskAssessment) {
        const overallScore = (intentStrength + contextScore) / 2;

        if (riskAssessment.level === 'high') {
            return 'require_verification';
        } else if (overallScore > 0.7) {
            return 'proceed_directly';
        } else if (overallScore > 0.4) {
            return 'provide_options';
        } else {
            return 'gather_more_info';
        }
    }

    generateReasoning(intentStrength, contextScore, riskAssessment) {
        const reasons = [];

        if (intentStrength > 0.7) {
            reasons.push('用户意图明确强烈');
        } else if (intentStrength < 0.3) {
            reasons.push('用户意图不够明确');
        }

        if (contextScore > 0.7) {
            reasons.push('对话上下文相关性高');
        } else if (contextScore < 0.3) {
            reasons.push('需要更多上下文信息');
        }

        if (riskAssessment.level === 'high') {
            reasons.push('存在高风险因素: ' + riskAssessment.reasons.join(', '));
        }

        return reasons.join('; ');
    }

    // 开始服务流程
    async startService(serviceType) {
        this.state.currentService = serviceType;
        this.showThinking();
        
        // 根据服务类型显示不同的思维过程
        const thinkingSteps = this.getThinkingSteps(serviceType);
        await this.displayThinkingProcess(thinkingSteps);
        
        // 隐藏思维过程，显示结果（移除自动跳转）
        setTimeout(() => {
            this.hideThinking();
            this.handleServiceRequest(serviceType);
        }, thinkingSteps.length * 600 + 1000);
    }

    getThinkingSteps(serviceType) {
        const currentCard = this.state.getCurrentCard();
        const stepsMap = {
            'subscribe': [
                {
                    text: `检测到您使用的是${currentCard.type}`,
                    type: 'detection',
                    confidence: 98
                },
                {
                    text: `分析账户使用模式：月均${currentCard.monthlyTransactions}笔交易`,
                    type: 'analysis',
                    confidence: 95
                },
                {
                    text: '基于交易频率，建议设置50元通知阈值',
                    type: 'recommendation',
                    confidence: 92
                },
                {
                    text: '生成3套个性化方案供您选择',
                    type: 'generation',
                    confidence: 96
                },
                {
                    text: '准备签约材料和风险评估',
                    type: 'preparation',
                    confidence: 99
                }
            ],
            'modify': [
                {
                    text: '查询您的现有服务配置',
                    type: 'query',
                    confidence: 100
                },
                {
                    text: '分析最近30天通知频率',
                    type: 'analysis',
                    confidence: 94
                },
                {
                    text: '识别可优化的设置项',
                    type: 'optimization',
                    confidence: 88
                },
                {
                    text: '生成个性化修改建议',
                    type: 'recommendation',
                    confidence: 91
                }
            ],
            'cancel': [
                {
                    text: '扫描所有已签约服务',
                    type: 'scan',
                    confidence: 100
                },
                {
                    text: '分析各卡片活跃度',
                    type: 'analysis',
                    confidence: 96
                },
                {
                    text: '计算已产生费用和潜在节省',
                    type: 'calculation',
                    confidence: 98
                },
                {
                    text: '评估解约对您的影响',
                    type: 'evaluation',
                    confidence: 89
                }
            ]
        };

        return stepsMap[serviceType] || [];
    }

    showThinking() {
        const thinkingDiv = document.getElementById('ai-thinking');
        const thinkingContent = document.getElementById('thinking-content');

        if (thinkingDiv && thinkingContent) {
            thinkingContent.innerHTML = '';
            thinkingDiv.classList.remove('hidden');
            thinkingDiv.classList.add('animate-fade-in-up');
            this.isThinking = true;

            // 滚动到思维过程区域（现在位于对话区域底部）
            setTimeout(() => {
                thinkingDiv.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }, 100);
        }
    }

    async displayThinkingProcess(steps) {
        const thinkingContent = document.getElementById('thinking-content');
        if (!thinkingContent) {
            return;
        }

        for (let i = 0; i < steps.length; i++) {
            await this.delay(600); // 减少延迟时间

            const step = steps[i];
            const stepElement = document.createElement('div');
            stepElement.className = 'thinking-step';

            // 根据步骤类型选择图标和颜色
            const iconMap = {
                'detection': { icon: '🔍', color: 'text-blue-400' },
                'analysis': { icon: '📊', color: 'text-purple-400' },
                'recommendation': { icon: '💡', color: 'text-yellow-400' },
                'generation': { icon: '⚡', color: 'text-green-400' },
                'preparation': { icon: '📋', color: 'text-cyan-400' },
                'query': { icon: '🔎', color: 'text-blue-400' },
                'optimization': { icon: '⚙️', color: 'text-orange-400' },
                'scan': { icon: '🔍', color: 'text-blue-400' },
                'calculation': { icon: '🧮', color: 'text-green-400' },
                'evaluation': { icon: '⚖️', color: 'text-purple-400' }
            };

            // 智能选择图标基于文本内容
            let stepInfo = { icon: '▸', color: 'text-blue-400' };
            if (step.type) {
                stepInfo = iconMap[step.type] || stepInfo;
            } else {
                // 基于文本内容智能选择图标
                const text = step.text.toLowerCase();
                if (text.includes('分析') || text.includes('评估')) {
                    stepInfo = { icon: '📊', color: 'text-purple-400' };
                } else if (text.includes('识别') || text.includes('检测') || text.includes('扫描')) {
                    stepInfo = { icon: '🔍', color: 'text-blue-400' };
                } else if (text.includes('生成') || text.includes('准备') || text.includes('创建')) {
                    stepInfo = { icon: '⚡', color: 'text-green-400' };
                } else if (text.includes('计算') || text.includes('费用')) {
                    stepInfo = { icon: '🧮', color: 'text-green-400' };
                } else if (text.includes('建议') || text.includes('推荐')) {
                    stepInfo = { icon: '💡', color: 'text-yellow-400' };
                } else if (text.includes('整合') || text.includes('确定')) {
                    stepInfo = { icon: '⚙️', color: 'text-orange-400' };
                }
            }

            stepElement.innerHTML = `
                <div class="flex items-center space-x-3 w-full">
                    <span class="${stepInfo.color} text-lg">${stepInfo.icon}</span>
                    <div class="flex-1">
                        <span class="text-gray-300">${step.text}</span>
                        <div class="flex items-center mt-1 space-x-2">
                            <div class="w-16 bg-gray-700 rounded-full h-1">
                                <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-1 rounded-full transition-all duration-1000"
                                     style="width: ${step.confidence}%"></div>
                            </div>
                            <span class="text-xs text-gray-500">${step.confidence}%</span>
                        </div>
                    </div>
                    <div class="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                        <svg class="w-3 h-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
            `;

            thinkingContent.appendChild(stepElement);

            // 滚动到新添加的步骤
            setTimeout(() => {
                stepElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }, 100);

            // 添加进度条动画
            setTimeout(() => {
                const progressBar = stepElement.querySelector('.bg-gradient-to-r');
                if (progressBar) {
                    progressBar.style.width = '0%';
                    setTimeout(() => {
                        progressBar.style.width = step.confidence + '%';
                    }, 100);
                }
            }, 200);
        }
    }

    hideThinking() {
        const thinkingDiv = document.getElementById('ai-thinking');
        if (thinkingDiv) {
            // 简化隐藏过程，减少延迟
            thinkingDiv.classList.add('hidden');
            this.isThinking = false;

            // 智能滚动到对话区域底部，显示后续内容
            this.smartScrollToBottom();
        }
    }

    handleServiceRequest(serviceType) {
        switch(serviceType) {
            case 'subscribe':
                this.showSubscriptionOptions();
                break;
            case 'modify':
                this.showModificationOptions();
                break;
            case 'cancel':
                this.showCancellationOptions();
                break;
        }
    }

    showSubscriptionOptions() {
        const currentCard = this.state.getCurrentCard();
        const recommendations = this.state.getRecommendations();

        // 基于用户数据生成个性化方案
        const solutions = this.generatePersonalizedSolutions(currentCard, recommendations);

        // 添加方案说明
        this.addAIMessage(`
            <div class="mb-4">
                <h4 class="font-semibold mb-2">🎯 为您量身定制的方案</h4>
                <p class="text-sm text-gray-400">基于您的${currentCard.type}使用习惯，我为您推荐以下方案：</p>
            </div>
        `);

        // 移除自动跳转，等待用户选择
        this.addAIMessage(`
            <div class="text-center mt-4">
                <button onclick="aiAgent.displaySolutionCards(${JSON.stringify(solutions).replace(/"/g, '&quot;')})"
                        class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                    查看推荐方案
                </button>
            </div>
        `);
    }

    generatePersonalizedSolutions(currentCard, recommendations) {
        const baseThreshold = this.calculateOptimalThreshold(currentCard);

        const solutions = [
            {
                title: '🌟 AI智能推荐',
                description: `收入全额通知，支出≥${baseThreshold}元通知`,
                price: '2元/月',
                recommended: true,
                confidence: 95,
                features: [
                    '工资到账即时提醒',
                    `过滤${baseThreshold}元以下小额消费`,
                    '余额不足智能预警',
                    '异常交易实时监控'
                ],
                savings: `预计减少${Math.round(currentCard.monthlyTransactions * 0.6)}条无效通知/月`,
                aiReason: `基于您月均${currentCard.monthlyTransactions}笔交易的分析结果`
            },
            {
                title: '📊 全面监控方案',
                description: '所有交易都通知，全面掌控',
                price: '2元/月',
                confidence: 70,
                features: [
                    '所有收入提醒',
                    '所有支出提醒',
                    '实时余额更新',
                    '交易分类统计'
                ],
                warnings: ['可能产生较多通知'],
                aiReason: '适合需要详细了解每笔交易的用户'
            },
            {
                title: '⚙️ 自定义方案',
                description: '完全按您的需求定制',
                price: '2元/月',
                confidence: 0,
                features: [
                    '自定义金额阈值',
                    '选择通知时间段',
                    '个性化通知内容',
                    '多渠道通知选择'
                ],
                customizable: true,
                aiReason: '为有特殊需求的用户提供灵活配置'
            }
        ];

        // 如果有费用优化建议，添加经济型方案
        const costOptimization = recommendations.find(r => r.type === 'cost_optimization');
        if (costOptimization) {
            solutions.push({
                title: '💰 经济优化方案',
                description: '最大化节省费用',
                price: '2元/月',
                confidence: 88,
                features: [
                    '仅重要交易通知',
                    '智能费用分析',
                    '节省建议推送'
                ],
                savings: `年节省约${costOptimization.savings}元`,
                aiReason: '基于您的多卡使用情况优化'
            });
        }

        return solutions;
    }

    calculateOptimalThreshold(card) {
        // 基于用户交易模式计算最优阈值
        const { monthlyTransactions, balance, type } = card;

        let threshold = 50; // 默认阈值

        // 根据卡片类型调整
        if (type === '工资卡') {
            threshold = Math.max(30, Math.min(100, balance * 0.01));
        } else if (type === '信用卡') {
            threshold = Math.max(100, Math.min(500, Math.abs(balance) * 0.1));
        } else if (type === '储蓄卡') {
            threshold = Math.max(20, Math.min(200, balance * 0.005));
        }

        // 根据交易频率调整
        if (monthlyTransactions > 20) {
            threshold *= 1.5; // 高频用户提高阈值
        } else if (monthlyTransactions < 10) {
            threshold *= 0.7; // 低频用户降低阈值
        }

        return Math.round(threshold);
    }

    showModificationOptions() {
        this.addAIMessage('我来帮您查看当前的服务设置...', () => {
            const currentCard = this.state.getCurrentCard();
            this.addAIMessage(`
                <div class="space-y-4">
                    <div class="bg-gray-800 rounded-lg p-4">
                        <h4 class="font-semibold mb-3 flex items-center">
                            <span class="text-blue-400 mr-2">⚙️</span>
                            当前设置
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400">通知金额阈值:</span>
                                <span class="text-blue-400 font-semibold">≥100元</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400">绑定手机:</span>
                                <span class="text-blue-400 font-semibold">138****8899</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400">通知时间:</span>
                                <span class="text-blue-400 font-semibold">全天候</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400">月费:</span>
                                <span class="text-green-400 font-semibold">2元</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-400 mb-2">🔍 AI分析建议</h4>
                        <p class="text-sm text-gray-300 mb-3">基于您最近的交易模式分析：</p>
                        <ul class="text-sm text-gray-400 space-y-1">
                            <li>• 当前100元阈值过高，错过了${Math.round(currentCard.monthlyTransactions * 0.3)}笔重要交易</li>
                            <li>• 建议调整为50元，可提升监控效果85%</li>
                            <li>• 预计每月增加3-5条有价值通知</li>
                        </ul>
                    </div>
                </div>
            `);

            // 移除自动跳转，等待用户确认
            this.addAIMessage(`
                <div class="text-center mt-4">
                    <button onclick="aiAgent.showModificationInterface()"
                            class="px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
                        开始修改设置
                    </button>
                </div>
            `);
        });
    }

    showModificationInterface() {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-purple-400 mb-4">🛠️ 修改服务设置</h4>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">通知金额阈值</label>
                        <div class="flex items-center space-x-3">
                            <input type="range" id="modify-threshold" min="10" max="500" value="100"
                                   class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <span id="modify-threshold-value" class="text-purple-400 font-semibold min-w-[70px]">100元</span>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">当前: 100元 → 建议: 50元</div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">通知时间设置</label>
                        <div class="grid grid-cols-2 gap-2">
                            <label class="flex items-center space-x-2">
                                <input type="radio" name="notify-time" value="all" checked class="text-purple-500">
                                <span class="text-sm">全天候通知</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="radio" name="notify-time" value="business" class="text-purple-500">
                                <span class="text-sm">工作时间</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">手机号码</label>
                        <input type="tel" value="138****8899" disabled
                               class="w-full bg-gray-700 rounded-lg px-3 py-2 text-gray-400 cursor-not-allowed">
                        <div class="text-xs text-gray-500 mt-1">如需修改手机号，请联系客服</div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="aiAgent.cancelModification()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                        取消
                    </button>
                    <button onclick="aiAgent.confirmModification()"
                            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
                        确认修改
                    </button>
                </div>
            </div>
        `);

        // 添加滑块交互
        setTimeout(() => {
            const slider = document.getElementById('modify-threshold');
            const valueDisplay = document.getElementById('modify-threshold-value');

            if (slider && valueDisplay) {
                slider.addEventListener('input', (e) => {
                    valueDisplay.textContent = e.target.value + '元';
                });
            }
        }, 100);
    }

    cancelModification() {
        this.addAIMessage('已取消修改操作。如有其他需要，请随时告诉我。');
    }

    confirmModification() {
        const threshold = document.getElementById('modify-threshold')?.value || 100;
        const timeOption = document.querySelector('input[name="notify-time"]:checked')?.value || 'all';

        this.addAIMessage('正在保存您的设置修改...');

        setTimeout(() => {
            this.addAIMessage(`
                <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                    <h4 class="font-semibold text-green-400 mb-3">✅ 修改成功</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">新通知阈值:</span>
                            <span class="text-green-400">≥${threshold}元</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">通知时间:</span>
                            <span class="text-green-400">${timeOption === 'all' ? '全天候' : '工作时间'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">生效时间:</span>
                            <span class="text-green-400">立即生效</span>
                        </div>
                    </div>
                    <div class="mt-3 text-xs text-gray-400">
                        💡 设置已保存，您将按新规则接收通知
                    </div>
                </div>
            `);
        }, 1500);
    }

    showCancellationOptions() {
        this.addAIMessage('正在智能分析您的服务使用情况...', () => {
            const cards = this.state.userData.cards;
            const totalMonthlyCost = cards.filter(c => c.hasService).length * 2;

            this.addAIMessage(`
                <div class="space-y-4">
                    <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-400 mb-3">📊 服务使用分析</h4>
                        <div class="space-y-3">
                            ${cards.map(card => `
                                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 ${card.hasService ? 'bg-green-500/20' : 'bg-gray-500/20'} rounded-lg flex items-center justify-center">
                                            <span class="text-xs">${card.type.charAt(0)}</span>
                                        </div>
                                        <div>
                                            <div class="font-medium">**** ${card.number} (${card.type})</div>
                                            <div class="text-xs text-gray-400">
                                                ${card.hasService ? '已开通服务' : '未开通'} •
                                                ${card.lastActivity ? `最后活动: ${Math.floor((Date.now() - card.lastActivity.getTime()) / (24*60*60*1000))}天前` : '活跃使用'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        ${card.hasService ? `
                                            <div class="text-sm font-semibold ${card.lastActivity && (Date.now() - card.lastActivity.getTime()) > 60*24*60*60*1000 ? 'text-red-400' : 'text-green-400'}">
                                                ${card.lastActivity && (Date.now() - card.lastActivity.getTime()) > 60*24*60*60*1000 ? '建议解约' : '建议保留'}
                                            </div>
                                            <div class="text-xs text-gray-400">2元/月</div>
                                        ` : `
                                            <div class="text-sm text-gray-500">未开通</div>
                                        `}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-400 mb-3">💰 费用优化建议</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">当前月费:</span>
                                <span class="text-yellow-400">${totalMonthlyCost}元</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">建议解约:</span>
                                <span class="text-red-400">尾号6789储蓄卡</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">预计年节省:</span>
                                <span class="text-green-400">24元</span>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            // 移除自动跳转，等待用户确认
            this.addAIMessage(`
                <div class="text-center mt-4">
                    <button onclick="aiAgent.showCancellationInterface()"
                            class="px-6 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                        继续解约流程
                    </button>
                </div>
            `);
        });
    }

    showCancellationInterface() {
        this.addAIMessage(`
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-red-400 mb-4">🗑️ 解约确认</h4>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-red-400">⚠️</span>
                        </div>
                        <div>
                            <div class="font-medium">即将解约服务</div>
                            <div class="text-sm text-gray-400">**** 6789 储蓄卡</div>
                        </div>
                    </div>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">解约原因:</span>
                            <span class="text-red-400">长期未使用</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">已产生费用:</span>
                            <span class="text-yellow-400">6元 (3个月)</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">解约后节省:</span>
                            <span class="text-green-400">24元/年</span>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3 mb-4">
                    <div class="flex items-start space-x-2">
                        <span class="text-blue-400 mt-0.5">ℹ️</span>
                        <div class="text-sm text-gray-300">
                            <p class="font-medium mb-1">解约说明：</p>
                            <ul class="space-y-1 text-xs text-gray-400">
                                <li>• 解约后将不再收到该卡的交易通知</li>
                                <li>• 如需重新开通，可随时联系我们</li>
                                <li>• 解约不影响其他卡片的服务</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button onclick="aiAgent.cancelCancellation()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                        取消操作
                    </button>
                    <button onclick="aiAgent.confirmCancellation()"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                        确认解约
                    </button>
                </div>
            </div>
        `);
    }

    cancelCancellation() {
        this.addAIMessage('已取消解约操作。您的服务将继续正常运行。');
    }

    confirmCancellation() {
        this.addAIMessage('正在处理解约申请...');

        setTimeout(() => {
            // 更新卡片状态
            this.state.updateCardService('6789', false);

            this.addAIMessage(`
                <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                    <h4 class="font-semibold text-green-400 mb-3">✅ 解约成功</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">解约卡号:</span>
                            <span class="text-green-400">**** 6789</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">解约时间:</span>
                            <span class="text-green-400">${new Date().toLocaleString('zh-CN')}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">预计年节省:</span>
                            <span class="text-green-400">24元</span>
                        </div>
                    </div>
                    <div class="mt-3 text-xs text-gray-400">
                        💡 解约已生效，该卡将不再产生银信通费用
                    </div>
                </div>
            `);
        }, 1500);
    }

    displaySolutionCards(solutions) {
        const container = document.getElementById('solution-cards');
        if (!container) return;

        container.innerHTML = '';
        container.classList.remove('hidden');
        container.classList.add('animate-fade-in-up');

        solutions.forEach((solution, index) => {
            const card = document.createElement('div');
            card.className = `solution-card ${solution.recommended ? 'recommended' : ''} ${solution.customizable ? 'customizable' : ''}`;

            const warningsHtml = solution.warnings ? `
                <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-2 mb-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-yellow-400">⚠️</span>
                        <span class="text-xs text-yellow-300">${solution.warnings.join(', ')}</span>
                    </div>
                </div>
            ` : '';

            const savingsHtml = solution.savings ? `
                <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-2 mb-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">💰</span>
                        <span class="text-xs text-green-300">${solution.savings}</span>
                    </div>
                </div>
            ` : '';

            const aiReasonHtml = solution.aiReason ? `
                <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-2 mb-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-blue-400">🤖</span>
                        <span class="text-xs text-blue-300">${solution.aiReason}</span>
                    </div>
                </div>
            ` : '';

            card.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <h4 class="text-lg font-semibold">${solution.title}</h4>
                    <div class="flex flex-col items-end space-y-1">
                        ${solution.confidence > 0 ? `
                            <div class="flex items-center space-x-2">
                                <div class="w-12 bg-gray-700 rounded-full h-1">
                                    <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-1 rounded-full"
                                         style="width: ${solution.confidence}%"></div>
                                </div>
                                <span class="text-xs text-gray-400">${solution.confidence}%</span>
                            </div>
                        ` : ''}
                        ${solution.recommended ? '<div class="badge success">推荐</div>' : ''}
                        ${solution.customizable ? '<div class="badge primary">可定制</div>' : ''}
                    </div>
                </div>

                <p class="text-gray-300 mb-4">${solution.description}</p>

                ${warningsHtml}
                ${savingsHtml}
                ${aiReasonHtml}

                ${solution.features ? `
                    <div class="mb-4">
                        <h5 class="text-sm font-semibold text-gray-300 mb-2">方案特色：</h5>
                        <ul class="text-sm text-gray-400 space-y-1">
                            ${solution.features.map(feature => `
                                <li class="flex items-center space-x-2">
                                    <span class="text-green-400">✓</span>
                                    <span>${feature}</span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                ` : ''}

                <div class="flex justify-between items-center pt-3 border-t border-gray-700">
                    <div>
                        <span class="text-xl font-bold text-blue-400">${solution.price}</span>
                        <div class="text-xs text-gray-500">首月免费</div>
                    </div>
                    <button onclick="aiAgent.selectSolution(${index}, ${JSON.stringify(solution).replace(/"/g, '&quot;')})"
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-all hover:scale-105 font-medium">
                        ${solution.customizable ? '定制方案' : '选择此方案'}
                    </button>
                </div>
            `;

            container.appendChild(card);

            // 添加延迟动画
            setTimeout(() => {
                card.classList.add('animate-fade-in-scale');
            }, index * 300);
        });

        // 添加方案对比提示
        setTimeout(() => {
            this.addComparisonTip(solutions);
        }, solutions.length * 300 + 500);
    }

    addComparisonTip(solutions) {
        const container = document.getElementById('solution-cards');
        if (!container) return;

        const tipElement = document.createElement('div');
        tipElement.className = 'bg-gray-800/50 rounded-lg p-3 mt-4 border border-gray-700';
        tipElement.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2 text-sm text-gray-400">
                    <span class="text-blue-400">💡</span>
                    <span>提示：您可以随时修改方案设置，首月免费体验</span>
                </div>
                <button onclick="aiAgent.showDetailedComparison(${JSON.stringify(solutions).replace(/"/g, '&quot;')})"
                        class="text-blue-400 hover:text-blue-300 text-sm transition-colors">
                    详细对比 →
                </button>
            </div>
        `;

        container.appendChild(tipElement);
        tipElement.classList.add('animate-fade-in');
    }

    showDetailedComparison(solutions) {
        this.addAIMessage(`
            <div class="bg-gradient-to-br from-purple-900/20 to-blue-900/20 border border-purple-600/30 rounded-xl p-6">
                <h4 class="text-xl font-bold text-purple-400 mb-6 flex items-center">
                    <span class="mr-3">📊</span>
                    方案详细对比
                </h4>

                <!-- 对比表格 -->
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="text-left py-3 text-gray-400">对比项目</th>
                                ${solutions.map(solution => `
                                    <th class="text-center py-3 px-2 ${solution.recommended ? 'text-green-400' : 'text-gray-300'}">
                                        ${solution.title}
                                        ${solution.recommended ? '<div class="text-xs text-green-400 mt-1">推荐</div>' : ''}
                                    </th>
                                `).join('')}
                            </tr>
                        </thead>
                        <tbody class="space-y-2">
                            <tr class="border-b border-gray-800">
                                <td class="py-3 text-gray-400">月费用</td>
                                ${solutions.map(solution => `
                                    <td class="text-center py-3 font-semibold text-blue-400">${solution.price}</td>
                                `).join('')}
                            </tr>
                            <tr class="border-b border-gray-800">
                                <td class="py-3 text-gray-400">AI置信度</td>
                                ${solutions.map(solution => `
                                    <td class="text-center py-3">
                                        ${solution.confidence > 0 ? `
                                            <div class="flex items-center justify-center space-x-2">
                                                <div class="w-12 bg-gray-700 rounded-full h-1">
                                                    <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-1 rounded-full"
                                                         style="width: ${solution.confidence}%"></div>
                                                </div>
                                                <span class="text-xs">${solution.confidence}%</span>
                                            </div>
                                        ` : '<span class="text-gray-500">自定义</span>'}
                                    </td>
                                `).join('')}
                            </tr>
                            <tr class="border-b border-gray-800">
                                <td class="py-3 text-gray-400">通知频率</td>
                                ${solutions.map(solution => {
                                    let frequency = '中等';
                                    if (solution.title.includes('智能')) frequency = '适中';
                                    else if (solution.title.includes('全面')) frequency = '较高';
                                    else if (solution.title.includes('自定义')) frequency = '可调';
                                    else if (solution.title.includes('经济')) frequency = '较低';

                                    return `<td class="text-center py-3 text-gray-300">${frequency}</td>`;
                                }).join('')}
                            </tr>
                            <tr class="border-b border-gray-800">
                                <td class="py-3 text-gray-400">适用场景</td>
                                ${solutions.map(solution => {
                                    let scenario = '通用';
                                    if (solution.title.includes('智能')) scenario = '工资卡用户';
                                    else if (solution.title.includes('全面')) scenario = '高频交易';
                                    else if (solution.title.includes('自定义')) scenario = '特殊需求';
                                    else if (solution.title.includes('经济')) scenario = '多卡用户';

                                    return `<td class="text-center py-3 text-gray-300 text-xs">${scenario}</td>`;
                                }).join('')}
                            </tr>
                            <tr>
                                <td class="py-3 text-gray-400">推荐指数</td>
                                ${solutions.map(solution => {
                                    const stars = solution.recommended ? 5 :
                                                 solution.confidence > 80 ? 4 :
                                                 solution.confidence > 60 ? 3 :
                                                 solution.customizable ? 3 : 2;

                                    return `
                                        <td class="text-center py-3">
                                            <div class="text-yellow-400">
                                                ${'★'.repeat(stars)}${'☆'.repeat(5-stars)}
                                            </div>
                                        </td>
                                    `;
                                }).join('')}
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 智能推荐说明 -->
                <div class="mt-6 bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                    <h5 class="font-semibold text-blue-400 mb-2">🤖 AI推荐理由</h5>
                    <div class="text-sm text-gray-300 space-y-2">
                        ${solutions.filter(s => s.recommended).map(solution => `
                            <div class="flex items-start space-x-2">
                                <span class="text-green-400 mt-0.5">✓</span>
                                <div>
                                    <span class="font-medium">${solution.title}：</span>
                                    <span>${solution.aiReason || '基于您的使用习惯智能推荐'}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="aiAgent.hideComparison()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                        返回选择
                    </button>
                    <button onclick="aiAgent.selectRecommended(${JSON.stringify(solutions.find(s => s.recommended)).replace(/"/g, '&quot;')})"
                            class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors font-medium">
                        选择推荐方案
                    </button>
                </div>
            </div>
        `);
    }

    hideComparison() {
        // 重新显示方案选择
        const solutionCards = document.getElementById('solution-cards');
        if (solutionCards) {
            solutionCards.classList.remove('hidden');
            solutionCards.scrollIntoView({ behavior: 'smooth' });
        }
    }

    selectRecommended(solution) {
        this.addAIMessage(`✅ 已选择推荐方案：${solution.title}`);
        this.selectSolution(0, solution);
    }

    selectSolution(index, solutionData = null) {
        // 记录用户选择
        this.state.addToHistory('solution_selected', { index, solutionData });
        this.state.incrementInteraction();

        // 如果是自定义方案，显示定制界面
        if (solutionData && solutionData.customizable) {
            this.showCustomizationInterface(solutionData);
            return;
        }

        // 显示确认信息
        this.showConfirmationSummary(solutionData || { title: '选中方案', price: '2元/月' });

        // 隐藏方案选择
        const solutionCards = document.getElementById('solution-cards');
        if (solutionCards) {
            solutionCards.classList.add('hidden');
        }

        // 移除自动执行，等待用户确认
        this.addAIMessage(`
            <div class="text-center mt-4">
                <button onclick="aiAgent.executeProcess(${JSON.stringify(solutionData).replace(/"/g, '&quot;')})"
                        class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                    确认办理
                </button>
            </div>
        `);
    }

    showCustomizationInterface(solution) {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-purple-400 mb-3">🎛️ 自定义您的通知方案</h4>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">通知金额阈值</label>
                        <div class="flex items-center space-x-3">
                            <input type="range" id="threshold-slider" min="10" max="500" value="50"
                                   class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <span id="threshold-value" class="text-blue-400 font-semibold min-w-[60px]">50元</span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">通知时间</label>
                        <div class="grid grid-cols-2 gap-2">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" checked class="rounded">
                                <span class="text-sm">工作日 9:00-18:00</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" checked class="rounded">
                                <span class="text-sm">全天候通知</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">通知类型</label>
                        <div class="space-y-2">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" checked class="rounded">
                                <span class="text-sm">收入通知</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" checked class="rounded">
                                <span class="text-sm">支出通知</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" class="rounded">
                                <span class="text-sm">余额预警</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="aiAgent.cancelCustomization()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                        返回
                    </button>
                    <button onclick="aiAgent.confirmCustomization()"
                            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
                        确认定制
                    </button>
                </div>
            </div>
        `);

        // 添加滑块交互
        setTimeout(() => {
            const slider = document.getElementById('threshold-slider');
            const valueDisplay = document.getElementById('threshold-value');

            if (slider && valueDisplay) {
                slider.addEventListener('input', (e) => {
                    valueDisplay.textContent = e.target.value + '元';
                });
            }
        }, 100);
    }

    cancelCustomization() {
        // 重新显示方案选择
        const solutionCards = document.getElementById('solution-cards');
        if (solutionCards) {
            solutionCards.classList.remove('hidden');
        }
    }

    confirmCustomization() {
        const threshold = document.getElementById('threshold-slider')?.value || 50;

        const customSolution = {
            title: '个人定制方案',
            description: `支出≥${threshold}元通知，个性化设置`,
            price: '2元/月',
            threshold: threshold
        };

        this.addAIMessage('✅ 定制方案已确认！');

        // 移除自动执行，等待用户最终确认
        this.addAIMessage(`
            <div class="text-center mt-4">
                <button onclick="aiAgent.showConfirmationSummary(${JSON.stringify(customSolution).replace(/"/g, '&quot;')}); aiAgent.executeProcess(${JSON.stringify(customSolution).replace(/"/g, '&quot;')})"
                        class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                    确认办理
                </button>
            </div>
        `);
    }

    showConfirmationSummary(solution) {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-green-400 mb-3">📋 办理确认</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">选择方案:</span>
                        <span class="text-green-400">${solution.title}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">服务费用:</span>
                        <span class="text-green-400">${solution.price}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">生效时间:</span>
                        <span class="text-green-400">立即生效</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">首次扣费:</span>
                        <span class="text-green-400">下月1日</span>
                    </div>
                </div>
                <div class="mt-3 text-xs text-gray-400">
                    💡 首月免费体验，不满意可随时取消
                </div>
            </div>
        `);
    }

    async executeProcess(solutionData = null) {
        // 更新业务状态
        this.state.updateBusinessState({
            currentFlow: 'subscription',
            currentStep: 0,
            totalSteps: 6,
            flowData: solutionData
        });

        const steps = [
            {
                name: '身份验证',
                description: '验证用户身份信息',
                duration: 1200,
                riskLevel: 'low'
            },
            {
                name: '卡片信息读取',
                description: '读取银行卡详细信息',
                duration: 800,
                riskLevel: 'low'
            },
            {
                name: '手机号确认',
                description: '确认绑定手机号码',
                duration: 1000,
                riskLevel: 'medium'
            },
            {
                name: '协议生成',
                description: '生成个性化服务协议',
                duration: 1500,
                riskLevel: 'low'
            },
            {
                name: '系统提交',
                description: '提交银行核心系统',
                duration: 2000,
                riskLevel: 'medium'
            },
            {
                name: '确认完成',
                description: '等待系统确认并激活服务',
                duration: 1800,
                riskLevel: 'low'
            }
        ];

        const progressDiv = document.getElementById('execution-progress');
        const stepsContainer = document.getElementById('progress-steps');

        if (!progressDiv || !stepsContainer) return;

        progressDiv.classList.remove('hidden');
        progressDiv.classList.add('animate-fade-in-up');
        stepsContainer.innerHTML = '';

        // 创建步骤元素
        steps.forEach((step, index) => {
            const stepElement = document.createElement('div');
            stepElement.className = 'execution-step pending';
            stepElement.innerHTML = `
                <div class="step-icon">${index + 1}</div>
                <div class="flex-1">
                    <div class="font-medium">${step.name}</div>
                    <div class="text-xs text-gray-500">${step.description}</div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="step-status text-sm text-gray-400">待处理</div>
                    <div class="step-timer text-xs text-gray-500 hidden"></div>
                </div>
            `;
            stepsContainer.appendChild(stepElement);
        });

        // 逐步执行
        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            const stepElements = stepsContainer.querySelectorAll('.execution-step');
            const currentElement = stepElements[i];

            // 更新业务状态
            this.state.updateBusinessState({ currentStep: i + 1 });

            // 完成上一步
            if (i > 0) {
                const prevElement = stepElements[i - 1];
                prevElement.classList.remove('active');
                prevElement.classList.add('completed');
                prevElement.querySelector('.step-icon').innerHTML = '✓';
                prevElement.querySelector('.step-status').textContent = '已完成';
                prevElement.querySelector('.step-timer').classList.add('hidden');
            }

            // 激活当前步骤
            currentElement.classList.remove('pending');
            currentElement.classList.add('active');
            currentElement.querySelector('.step-status').textContent = '处理中...';

            // 显示处理时间
            const timer = currentElement.querySelector('.step-timer');
            timer.classList.remove('hidden');

            // 模拟处理时间
            let elapsed = 0;
            const interval = setInterval(() => {
                elapsed += 100;
                timer.textContent = `${(elapsed / 1000).toFixed(1)}s`;

                if (elapsed >= step.duration) {
                    clearInterval(interval);
                }
            }, 100);

            // 等待步骤完成
            await this.delay(step.duration);
            clearInterval(interval);

            // 模拟可能的错误（低概率）
            if (Math.random() < 0.05 && step.riskLevel === 'medium') {
                await this.handleStepError(currentElement, step, i);
                continue;
            }
        }

        // 完成最后一步
        const lastStep = stepsContainer.querySelectorAll('.execution-step')[steps.length - 1];
        lastStep.classList.remove('active');
        lastStep.classList.add('completed');
        lastStep.querySelector('.step-icon').innerHTML = '✓';
        lastStep.querySelector('.step-status').textContent = '已完成';
        lastStep.querySelector('.step-timer').classList.add('hidden');

        // 更新最终状态
        this.state.updateBusinessState({
            currentStep: steps.length,
            currentFlow: 'completed'
        });

        // 显示成功消息
        setTimeout(() => {
            this.showSuccessMessage(solutionData);
        }, 500);
    }

    async handleStepError(element, step, stepIndex) {
        element.classList.remove('active');
        element.classList.add('error');
        element.querySelector('.step-status').textContent = '处理失败';
        element.querySelector('.step-timer').classList.add('hidden');

        // 显示错误信息
        this.addAIMessage(`
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-red-400">⚠️</span>
                    <span class="font-semibold text-red-400">处理异常</span>
                </div>
                <p class="text-sm text-gray-300">${step.name}步骤遇到临时问题，正在自动重试...</p>
            </div>
        `);

        await this.delay(2000);

        // 重试
        element.classList.remove('error');
        element.classList.add('active');
        element.querySelector('.step-status').textContent = '重试中...';

        await this.delay(step.duration * 0.8);

        // 重试成功
        this.addAIMessage('✅ 重试成功，继续处理...');
    }

    showSuccessMessage(solutionData = null) {
        const businessId = 'BXT' + Date.now();
        const currentCard = this.state.getCurrentCard();

        // 记录完成的服务
        this.state.addCompletedService('subscription', {
            businessId,
            solution: solutionData,
            cardNumber: currentCard.number,
            timestamp: new Date()
        });

        // 计算预期效果
        const expectedNotifications = Math.round(currentCard.monthlyTransactions * 0.4);
        const savedNotifications = currentCard.monthlyTransactions - expectedNotifications;

        this.addAIMessage(`
            <div class="text-center">
                <div class="relative">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
                        <span class="text-white text-xs font-bold">AI</span>
                    </div>
                </div>

                <h3 class="text-2xl font-bold text-green-400 mb-2">🎉 办理成功！</h3>
                <p class="text-gray-300 mb-6">您的银信通服务已智能配置完成</p>

                <!-- 服务详情 -->
                <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 mb-6 border border-gray-700">
                    <h4 class="font-semibold text-gray-200 mb-4">📋 服务详情</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="text-left">
                            <div class="text-gray-400">业务凭证号</div>
                            <div class="font-mono text-blue-400 text-lg">${businessId}</div>
                        </div>
                        <div class="text-left">
                            <div class="text-gray-400">服务方案</div>
                            <div class="text-green-400">${solutionData?.title || 'AI智能推荐'}</div>
                        </div>
                        <div class="text-left">
                            <div class="text-gray-400">生效时间</div>
                            <div class="text-green-400">立即生效</div>
                        </div>
                        <div class="text-left">
                            <div class="text-gray-400">服务费用</div>
                            <div class="text-green-400">${solutionData?.price || '2元/月'}</div>
                        </div>
                    </div>
                </div>

                <!-- AI预测效果 -->
                <div class="bg-blue-900/20 border border-blue-600/30 rounded-xl p-4 mb-6">
                    <h4 class="font-semibold text-blue-400 mb-3">🤖 AI预测效果</h4>
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-green-400">${expectedNotifications}</div>
                            <div class="text-xs text-gray-400">月均有效通知</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-purple-400">${savedNotifications}</div>
                            <div class="text-xs text-gray-400">过滤无效通知</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-yellow-400">85%</div>
                            <div class="text-xs text-gray-400">智能过滤率</div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="grid grid-cols-2 gap-3 mb-4">
                    <button onclick="aiAgent.downloadCertificate('${businessId}')"
                            class="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-all hover:scale-105">
                        <span>📄</span>
                        <span>下载凭证</span>
                    </button>
                    <button onclick="aiAgent.viewServiceDetails('${businessId}')"
                            class="flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-all hover:scale-105">
                        <span>📊</span>
                        <span>查看详情</span>
                    </button>
                </div>

                <button onclick="aiAgent.startNewService()"
                        class="w-full px-4 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-all text-gray-300 hover:text-white">
                    🔄 继续办理其他业务
                </button>

                <!-- 满意度调查 -->
                <div class="mt-6 pt-4 border-t border-gray-700">
                    <p class="text-sm text-gray-400 mb-3">请为本次服务体验评分：</p>
                    <div class="flex justify-center space-x-2">
                        ${[1,2,3,4,5].map(star => `
                            <button onclick="aiAgent.rateSatisfaction(${star})"
                                    class="text-2xl text-gray-600 hover:text-yellow-400 transition-colors">
                                ⭐
                            </button>
                        `).join('')}
                    </div>
                </div>
            </div>
        `);

        // 更新账户状态
        this.state.updateCardService('8899', true);
        this.updateAccountInfo();

        // 显示庆祝动画
        this.showCelebrationAnimation();
    }

    downloadCertificate(businessId) {
        // 模拟下载凭证
        this.addAIMessage('📄 正在生成电子凭证，请稍候...');

        setTimeout(() => {
            this.addAIMessage(`
                <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">✅</span>
                        <span class="text-green-400">电子凭证已生成完成</span>
                    </div>
                    <p class="text-sm text-gray-400 mt-2">凭证编号：${businessId}</p>
                </div>
            `);
        }, 1500);
    }

    viewServiceDetails(businessId) {
        // 显示服务详情
        const currentCard = this.state.getCurrentCard();
        this.addAIMessage(`
            <div class="bg-gray-800 rounded-lg p-4">
                <h4 class="font-semibold mb-3">📊 服务详情</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">服务卡号:</span>
                        <span>**** ${currentCard.number}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">开通时间:</span>
                        <span>${new Date().toLocaleString('zh-CN')}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">服务状态:</span>
                        <span class="text-green-400">正常</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">下次扣费:</span>
                        <span>${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString('zh-CN')}</span>
                    </div>
                </div>
            </div>
        `);
    }

    rateSatisfaction(rating) {
        const messages = {
            1: '感谢您的反馈，我们会持续改进服务质量。',
            2: '感谢您的建议，我们会努力提升服务体验。',
            3: '感谢您的评价，我们会继续优化服务流程。',
            4: '感谢您的认可，我们会保持优质服务。',
            5: '感谢您的五星好评！我们会继续为您提供更好的服务。'
        };

        this.addAIMessage(`
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-yellow-400">${'⭐'.repeat(rating)}</span>
                    <span class="text-yellow-400 font-semibold">${rating}星评价</span>
                </div>
                <p class="text-sm text-gray-300">${messages[rating]}</p>
            </div>
        `);

        // 记录评分
        this.state.addToHistory('satisfaction_rating', { rating, timestamp: new Date() });
    }

    showCelebrationAnimation() {
        // 创建庆祝动画效果
        const celebration = document.createElement('div');
        celebration.className = 'fixed inset-0 pointer-events-none z-50';
        celebration.innerHTML = `
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-6xl animate-bounce">🎉</div>
            </div>
        `;

        document.body.appendChild(celebration);

        setTimeout(() => {
            celebration.remove();
        }, 3000);
    }

    startNewService() {
        // 重置界面
        document.getElementById('execution-progress')?.classList.add('hidden');
        document.getElementById('solution-cards')?.classList.add('hidden');
        
        this.addAIMessage('还有什么可以帮您的吗？您可以继续办理其他银信通服务。');
    }

    updateAccountInfo() {
        // 更新右侧账户信息面板
        const statusElement = document.getElementById('service-status');
        if (statusElement) {
            statusElement.textContent = '已开通';
            statusElement.className = 'badge success';

            // 添加成功动画
            statusElement.classList.add('animate-pulse');
            setTimeout(() => {
                statusElement.classList.remove('animate-pulse');
            }, 2000);
        }

        // 更新实时通知面板
        this.updateNotificationPanel();
    }

    updateNotificationPanel() {
        // 模拟更新通知数据
        const notificationElements = document.querySelectorAll('.bg-gray-800\\/50');
        if (notificationElements.length >= 3) {
            // 更新今日通知数
            const todayNotification = notificationElements[0]?.querySelector('.text-blue-400');
            if (todayNotification) {
                this.animateNumberChange(todayNotification, '3条', '4条');
            }

            // 更新节省通知数
            const savedNotification = notificationElements[2]?.querySelector('.text-purple-400');
            if (savedNotification) {
                this.animateNumberChange(savedNotification, '12条', '15条');
            }
        }
    }

    animateNumberChange(element, oldValue, newValue) {
        element.style.transform = 'scale(1.2)';
        element.style.color = '#10b981';

        setTimeout(() => {
            element.textContent = newValue;
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 300);
    }

    addAIMessage(content, callback) {
        const messagesDiv = document.getElementById('chat-messages');
        if (!messagesDiv) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = 'ai-message';
        messageDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span class="text-white text-sm font-bold">AI</span>
                </div>
                <div class="flex-1 rounded-2xl p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-primary);">
                    <div style="color: var(--text-primary);">${content}</div>
                </div>
            </div>
        `;

        messagesDiv.appendChild(messageDiv);

        // 智能滚动到最新消息
        this.smartScrollToBottom();

        // 如果用户不在底部，显示新消息提示
        if (!this.shouldAutoScroll()) {
            this.showNewMessageIndicator();
        }

        if (callback) {
            setTimeout(callback, 1500);
        }
    }

    // 平滑滚动到底部的方法
    scrollToBottom() {
        const messagesDiv = document.getElementById('chat-messages');
        const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
        if (!messagesDiv) return;

        // 使用 requestAnimationFrame 确保 DOM 更新后再滚动
        requestAnimationFrame(() => {
            // 平滑滚动到底部
            messagesDiv.scrollTo({
                top: messagesDiv.scrollHeight,
                behavior: 'smooth'
            });

            // 隐藏滚动到底部按钮并清除新消息提示
            if (scrollToBottomBtn) {
                scrollToBottomBtn.classList.add('hidden');
                scrollToBottomBtn.classList.remove('has-new-message');
            }
        });
    }

    // 检查是否需要滚动到底部（用户是否在底部附近）
    shouldAutoScroll() {
        const messagesDiv = document.getElementById('chat-messages');
        if (!messagesDiv) return true;

        const { scrollTop, scrollHeight, clientHeight } = messagesDiv;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        // 如果用户距离底部小于100px，则认为用户在底部附近，应该自动滚动
        return distanceFromBottom < 100;
    }

    // 初始化聊天区域滚动监听
    initializeChatScrollListener() {
        const messagesDiv = document.getElementById('chat-messages');
        const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
        if (!messagesDiv) return;

        let isUserScrolling = false;
        let scrollTimeout;
        let lastScrollCheck = 0;

        // 节流函数，防止过度频繁的滚动检查
        const throttledScrollCheck = () => {
            const now = Date.now();
            if (now - lastScrollCheck > 100) { // 最多每100ms检查一次
                this.toggleScrollToBottomButton();
                lastScrollCheck = now;
            }
        };

        // 监听用户滚动行为
        messagesDiv.addEventListener('scroll', () => {
            isUserScrolling = true;

            // 清除之前的超时
            clearTimeout(scrollTimeout);

            // 节流检查是否显示滚动到底部按钮
            throttledScrollCheck();

            // 500ms后重置用户滚动状态
            scrollTimeout = setTimeout(() => {
                isUserScrolling = false;
            }, 500);
        });

        // 存储滚动状态供其他方法使用
        this.isUserScrolling = () => isUserScrolling;
    }

    // 调整聊天容器高度以匹配右侧面板
    adjustChatContainerHeight() {
        const chatContainer = document.querySelector('.chat-container');
        const rightPanel = document.querySelector('.lg\\:col-span-1');

        if (!chatContainer || !rightPanel) return;

        // 等待DOM完全渲染后再计算
        setTimeout(() => {
            this.calculateAndSetChatHeight();

            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                this.calculateAndSetChatHeight();
            });
        }, 100);
    }

    // 计算并设置聊天容器高度
    calculateAndSetChatHeight() {
        const chatContainer = document.querySelector('.chat-container');
        const rightPanel = document.querySelector('.lg\\:col-span-1');

        if (!chatContainer || !rightPanel) return;

        // 检查是否为桌面端（大于768px）
        const isDesktop = window.innerWidth > 768;

        if (!isDesktop) {
            // 移动端不调整高度，使用CSS响应式设置
            return;
        }

        // 获取右侧面板的高度
        const rightPanelHeight = rightPanel.offsetHeight;

        // 设置聊天容器的高度，确保不小于最小高度
        const minHeight = 500;
        const newHeight = Math.max(rightPanelHeight, minHeight);

        chatContainer.style.height = `${newHeight}px`;

        console.log(`聊天容器高度已调整为: ${newHeight}px (右侧面板高度: ${rightPanelHeight}px)`);
    }

    // 当右侧面板内容变化时重新计算高度
    recalculateChatHeight() {
        // 延迟执行，确保DOM更新完成
        setTimeout(() => {
            this.calculateAndSetChatHeight();
        }, 100);
    }

    // 控制滚动到底部按钮的显示/隐藏
    toggleScrollToBottomButton() {
        const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
        if (!scrollToBottomBtn) return;

        const messagesDiv = document.getElementById('chat-messages');
        if (!messagesDiv) return;

        const { scrollTop, scrollHeight, clientHeight } = messagesDiv;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        // 如果距离底部超过150px，显示按钮；否则隐藏
        if (distanceFromBottom > 150) {
            scrollToBottomBtn.classList.remove('hidden');
        } else {
            scrollToBottomBtn.classList.add('hidden');
        }
    }

    // 智能滚动到底部（考虑用户滚动状态）
    smartScrollToBottom() {
        // 如果用户正在手动滚动，则不自动滚动
        if (this.isUserScrolling && this.isUserScrolling()) {
            return;
        }

        // 如果用户不在底部附近，也不自动滚动
        if (!this.shouldAutoScroll()) {
            return;
        }

        this.scrollToBottom();
    }

    sendMessage() {
        const input = document.getElementById('user-input');
        if (!input) return;

        const message = input.value.trim();
        if (!message) return;

        this.addUserMessage(message);
        input.value = '';

        // 简单的意图识别
        setTimeout(() => {
            this.processUserMessage(message);
        }, 1000);
    }

    addUserMessage(message) {
        const messagesDiv = document.getElementById('chat-messages');
        if (!messagesDiv) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = 'user-message';
        messageDiv.innerHTML = `
            <div class="flex items-start space-x-3 justify-end">
                <div class="flex-1 rounded-2xl p-4 max-w-md" style="background: var(--gradient-primary);">
                    <p class="text-white">${message}</p>
                </div>
                <div class="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0" style="background-color: var(--bg-quaternary);">
                    <span class="text-sm" style="color: var(--text-primary);">您</span>
                </div>
            </div>
        `;

        messagesDiv.appendChild(messageDiv);

        // 用户消息总是滚动到底部
        this.scrollToBottom();
    }

    // 显示新消息提示
    showNewMessageIndicator() {
        const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
        if (scrollToBottomBtn && !scrollToBottomBtn.classList.contains('hidden')) {
            scrollToBottomBtn.classList.add('has-new-message');
        }
    }

    // 清除新消息提示
    clearNewMessageIndicator() {
        const scrollToBottomBtn = document.getElementById('scroll-to-bottom');
        if (scrollToBottomBtn) {
            scrollToBottomBtn.classList.remove('has-new-message');
        }
    }

    processUserMessage(message) {
        const intent = this.analyzeIntent(message);
        const context = this.getConversationContext();

        // 显示AI正在分析的状态
        this.showTypingIndicator();

        setTimeout(() => {
            this.hideTypingIndicator();
            this.respondToIntent(intent, message, context);
        }, 1500);
    }

    analyzeIntent(message) {
        const lowerMessage = message.toLowerCase();

        // 扩展的业务意图识别
        const businessIntents = {
            // 银信通相关
            sms_notification: {
                keywords: ['银信通', '短信', '通知', '提醒', '消息', '开通', '签约'],
                businessType: 'sms_service',
                confidence: 0
            },

            // 转账汇款
            transfer: {
                keywords: ['转账', '汇款', '转钱', '付款', '收款', '转出', '转入'],
                businessType: 'transfer',
                confidence: 0
            },

            // 理财投资
            wealth_management: {
                keywords: ['理财', '投资', '基金', '股票', '债券', '定期', '收益', '财富'],
                businessType: 'wealth',
                confidence: 0
            },

            // 贷款申请
            loan: {
                keywords: ['贷款', '借款', '信贷', '房贷', '车贷', '消费贷', '申请'],
                businessType: 'loan',
                confidence: 0
            },

            // 信用卡服务
            credit_card: {
                keywords: ['信用卡', '信用', '额度', '分期', '还款', '账单', '积分'],
                businessType: 'credit_card',
                confidence: 0
            },

            // 账户管理
            account_management: {
                keywords: ['账户', '余额', '明细', '流水', '开户', '销户', '冻结', '解冻'],
                businessType: 'account',
                confidence: 0
            },

            // 外汇业务
            forex: {
                keywords: ['外汇', '汇率', '换汇', '结汇', '购汇', '美元', '欧元'],
                businessType: 'forex',
                confidence: 0
            },

            // 保险业务
            insurance: {
                keywords: ['保险', '保障', '意外险', '健康险', '寿险', '车险'],
                businessType: 'insurance',
                confidence: 0
            },

            // 通用操作意图
            modify: {
                keywords: ['修改', '更改', '调整', '设置', '变更', '改变'],
                businessType: 'modify',
                confidence: 0
            },
            cancel: {
                keywords: ['取消', '解约', '关闭', '停用', '注销', '退订'],
                businessType: 'cancel',
                confidence: 0
            },
            inquiry: {
                keywords: ['查询', '查看', '了解', '咨询', '问', '什么', '怎么样'],
                businessType: 'inquiry',
                confidence: 0
            },
            help: {
                keywords: ['帮助', '怎么', '如何', '教', '指导', '不会', '不懂'],
                businessType: 'help',
                confidence: 0
            }
        };

        // 计算每个意图的置信度
        Object.keys(businessIntents).forEach(intentKey => {
            const intent = businessIntents[intentKey];
            let matchCount = 0;
            intent.keywords.forEach(keyword => {
                if (lowerMessage.includes(keyword)) {
                    matchCount++;
                }
            });
            intent.confidence = matchCount / intent.keywords.length;
        });

        // 找到置信度最高的意图
        const bestIntentKey = Object.keys(businessIntents).reduce((best, current) => {
            return businessIntents[current].confidence > businessIntents[best].confidence ? current : best;
        });

        const bestIntent = businessIntents[bestIntentKey];

        return {
            intent: bestIntentKey,
            businessType: bestIntent.businessType,
            confidence: bestIntent.confidence,
            originalMessage: message,
            allIntents: businessIntents
        };
    }

    getConversationContext() {
        // 获取对话上下文
        const messages = document.querySelectorAll('.ai-message, .user-message');
        return {
            messageCount: messages.length,
            lastService: this.state.currentService,
            userPreferences: this.state.userData.preferences
        };
    }

    showTypingIndicator() {
        const messagesDiv = document.getElementById('chat-messages');
        if (!messagesDiv) return;

        const typingDiv = document.createElement('div');
        typingDiv.id = 'typing-indicator';
        typingDiv.className = 'ai-message';
        typingDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span class="text-white text-sm font-bold">AI</span>
                </div>
                <div class="flex-1 bg-gray-900 rounded-2xl p-4 border border-gray-800">
                    <div class="flex items-center space-x-2">
                        <span class="text-gray-400 text-sm">正在分析您的需求</span>
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-75"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        messagesDiv.appendChild(typingDiv);

        // 智能滚动到最新消息
        this.smartScrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    respondToIntent(intentData, originalMessage, context) {
        const { intent, businessType, confidence } = intentData;

        if (confidence > 0.2) {
            switch (businessType) {
                case 'sms_service':
                    // 银信通业务：直接显示专用选项，不显示其他业务
                    this.handleSmsServiceRequest(intent, originalMessage);
                    break;

                case 'transfer':
                    this.handleTransferRequest(originalMessage);
                    break;

                case 'wealth':
                    this.handleWealthManagementRequest(originalMessage);
                    break;

                case 'loan':
                    this.handleLoanRequest(originalMessage);
                    break;

                case 'credit_card':
                    this.handleCreditCardRequest(originalMessage);
                    break;

                case 'account':
                    this.handleAccountManagementRequest(originalMessage);
                    break;

                case 'forex':
                    this.handleForexRequest(originalMessage);
                    break;

                case 'insurance':
                    this.handleInsuranceRequest(originalMessage);
                    break;

                case 'inquiry':
                    this.handleGeneralInquiry(originalMessage);
                    break;

                case 'help':
                    this.showGeneralHelpMessage();
                    break;

                default:
                    this.showBusinessMenu(originalMessage);
            }
        } else {
            this.showBusinessMenu(originalMessage);
        }
    }

    handleInquiry(message) {
        const responses = [
            '银信通是银行短信提醒服务，可以在您的账户发生资金变动时及时通知您。',
            '我可以帮您智能办理开通、修改设置或取消服务，整个过程只需2-3分钟。',
            '相比传统柜台办理需要15-20分钟，我们的AI服务更快更便捷。'
        ];

        responses.forEach((response, index) => {
            setTimeout(() => {
                this.addAIMessage(response);
            }, index * 1500);
        });
    }

    showHelpMessage() {
        this.addAIMessage(`
            <div class="space-y-3">
                <h4 class="font-semibold">我可以帮您：</h4>
                <div class="grid grid-cols-1 gap-2 text-sm">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">✓</span>
                        <span>智能开通银信通服务</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-blue-400">✓</span>
                        <span>个性化修改通知设置</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-purple-400">✓</span>
                        <span>分析解约影响和建议</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-yellow-400">✓</span>
                        <span>费用优化和批量管理</span>
                    </div>
                </div>
                <p class="text-xs text-gray-400 mt-3">您可以直接告诉我您的需求，我会智能理解并为您提供最佳方案。</p>
            </div>
        `);
    }

    // 银信通服务处理 - 第二阶段：需求探索与方案共创
    handleSmsServiceRequest(intent, message) {
        // 根据不同的银信通服务类型进行处理
        let serviceType, serviceName;

        switch (intent) {
            case 'yxt_signup':
                serviceType = 'yxt_signup';
                serviceName = '银信通签约';
                break;
            case 'yxt_modify':
                serviceType = 'yxt_modify';
                serviceName = '银信通修改';
                break;
            case 'yxt_cancel':
                serviceType = 'yxt_cancel';
                serviceName = '银信通解约';
                break;
            default:
                serviceType = 'sms_notification';
                serviceName = '银信通服务';
        }

        // 第二阶段：需求探索与方案共创
        this.startNeedsDiscoveryProcess(serviceType, serviceName);
    }

    // 第二阶段：需求探索与方案共创
    async startNeedsDiscoveryProcess(type, serviceName) {
        // 1. 对话式需求挖掘
        await this.performConversationalNeedsDiscovery(type, serviceName);

        // 2. 智能需求明确度判断
        const needsClarityLevel = this.assessNeedsClarityLevel(type, serviceName);

        if (needsClarityLevel === 'clear') {
            // 需求明确，直接进入方案生成
            await this.proceedDirectlyToSolution(type, serviceName);
        } else {
            // 需求模糊，展示智能预测推荐
            await this.predictRelatedNeeds(type);
        }
    }

    // 需求明确度评估机制
    assessNeedsClarityLevel(type, serviceName) {
        const clearIndicators = {
            'sms_notification': [
                '银信通', '短信提醒', '短信通知', '开通银信通',
                '账户变动提醒', '余额提醒', '交易提醒'
            ],
            'wealth_management': [
                '理财产品', '投资理财', '定期存款', '基金',
                '购买理财', '理财规划'
            ],
            'transfer': [
                '转账', '汇款', '转钱', '付款',
                '给某某转账', '转账到'
            ]
        };

        const indicators = clearIndicators[type] || [];
        const userMessage = serviceName.toLowerCase();

        // 检查用户表达是否包含明确的业务关键词
        const hasExplicitIntent = indicators.some(indicator =>
            userMessage.includes(indicator.toLowerCase())
        );

        // 如果用户明确表达了具体业务需求，判定为明确
        return hasExplicitIntent ? 'clear' : 'ambiguous';
    }

    // 明确需求时直接进入方案生成
    async proceedDirectlyToSolution(type, serviceName) {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-green-400">🎯</span>
                    <span class="font-semibold text-green-400">需求明确识别</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">
                    我理解您要办理${serviceName}，需求非常明确。为了提高办事效率，我将直接为您生成解决方案。
                </p>
                <div class="bg-gray-800/50 rounded-lg p-3 mb-3">
                    <div class="font-medium text-gray-200 mb-1">核心需求：${serviceName}</div>
                    <div class="text-xs text-gray-400">明确度评估：95% - 无需额外推荐</div>
                </div>
                <div class="flex justify-center space-x-3">
                    <button onclick="aiAgent.generateDirectSolution('${type}')"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        直接生成方案
                    </button>
                    <button onclick="aiAgent.showOptionalRecommendations('${type}')"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        查看相关推荐
                    </button>
                </div>
            </div>
        `);
    }

    async performConversationalNeedsDiscovery(type, serviceName) {
        this.showThinking();

        // AI思维链可视化
        await this.displayThinkingProcess([
            { text: '分析用户历史行为模式', confidence: 98 },
            { text: '识别核心业务需求', confidence: 95 },
            { text: '预测潜在关联需求', confidence: 92 },
            { text: '评估用户风险偏好', confidence: 89 },
            { text: '生成个性化方案建议', confidence: 96 }
        ]);

        this.hideThinking();

        // 精准需求理解
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-blue-400">🎯</span>
                    <span class="font-semibold text-blue-400">需求理解</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">
                    我理解您想了解${serviceName}。基于您的账户活动分析，我发现了以下核心需求：
                </p>
                <div class="bg-gray-800/50 rounded-lg p-3 mb-3">
                    <div class="font-medium text-gray-200 mb-1">主要需求：${serviceName}</div>
                    <div class="text-xs text-gray-400">您希望及时了解账户变动情况</div>
                </div>
            </div>
        `);
    }

    async predictRelatedNeeds(primaryType) {
        await this.delay(1000);

        // 预测相关需求
        const relatedNeeds = this.generateRelatedNeeds(primaryType);

        // 获取业务类型的中文名称
        const businessNames = {
            'sms_notification': '银信通短信服务',
            'wealth_management': '理财投资服务',
            'transfer': '转账汇款服务'
        };

        const businessName = businessNames[primaryType] || '当前服务';

        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-purple-400">🔮</span>
                    <span class="font-semibold text-purple-400">智能预测推荐</span>
                </div>
                <p class="text-sm text-gray-300 mb-2">
                    基于您要办理的${businessName}，我发现以下相关服务可能对您有帮助：
                </p>
                <div class="text-xs text-gray-400 mb-4">
                    💡 所有推荐都与您的核心需求直接相关，确保业务协同效果
                </div>

                <div id="predicted-needs" class="space-y-3 mb-4">
                    <!-- 动态生成预测需求卡片 -->
                </div>

                <!-- 用户选择区域 -->
                <div class="border-t border-gray-700 pt-4">
                    <div class="flex justify-center space-x-3">
                        <button onclick="aiAgent.confirmPredictedNeeds()"
                                class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm">
                            📋 确认选择的服务
                        </button>
                        <button onclick="aiAgent.skipRecommendationsAndProceed('${primaryType}')"
                                class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm">
                            ⚡ 跳过推荐，直接办理
                        </button>
                    </div>
                    <div class="text-center mt-2">
                        <div class="text-xs text-gray-400">
                            您可以选择需要的相关服务，或直接办理主要业务
                        </div>
                    </div>
                </div>
            </div>
        `);

        // 生成预测需求卡片
        await this.delay(500);
        this.renderPredictedNeedsCards(relatedNeeds);
    }

    // 跳过推荐直接办理
    skipRecommendationsAndProceed(primaryType) {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-blue-400">⚡</span>
                    <span class="text-sm font-medium text-blue-400">高效办理模式</span>
                </div>
                <p class="text-sm text-gray-300">
                    已跳过相关推荐，专注办理您的核心需求。我将直接为您生成最优解决方案。
                </p>
            </div>
        `);

        // 直接进入方案生成
        setTimeout(() => {
            this.generateDirectSolution(primaryType);
        }, 1000);
    }

    // 直接生成解决方案（明确需求时）
    async generateDirectSolution(primaryType) {
        // 阶段一：动态生成初步方案
        await this.generateInitialSolution(primaryType);
    }

    // 阶段一：动态生成初步方案
    async generateInitialSolution(primaryType) {
        this.showThinking();

        // 根据服务类型调整思维过程
        let thinkingSteps, solutionSteps, todoList, serviceTitle;

        if (primaryType === 'yxt_signup') {
            thinkingSteps = [
                { text: '分析银信通签约需求', confidence: 100 },
                { text: '生成个性化签约方案', confidence: 98 },
                { text: '规划操作步骤流程', confidence: 95 },
                { text: '准备用户待办清单', confidence: 92 }
            ];
            solutionSteps = this.generateYxtSignupSteps();
            todoList = this.generateUserTodoList();
            serviceTitle = '银信通签约行动方案';
        } else if (primaryType === 'yxt_modify') {
            thinkingSteps = [
                { text: '分析银信通修改需求', confidence: 100 },
                { text: '生成个性化修改方案', confidence: 98 },
                { text: '规划修改步骤流程', confidence: 95 },
                { text: '准备修改操作清单', confidence: 92 }
            ];
            solutionSteps = this.generateYxtModifySteps();
            todoList = this.generateModifyTodoList();
            serviceTitle = '银信通修改行动方案';
        } else if (primaryType === 'yxt_cancel') {
            thinkingSteps = [
                { text: '分析银信通解约需求', confidence: 100 },
                { text: '生成个性化解约方案', confidence: 98 },
                { text: '规划解约步骤流程', confidence: 95 },
                { text: '准备解约操作清单', confidence: 92 }
            ];
            solutionSteps = this.generateYxtCancelSteps();
            todoList = this.generateCancelTodoList();
            serviceTitle = '银信通解约行动方案';
        } else {
            // 默认为签约
            thinkingSteps = [
                { text: '分析银信通服务需求', confidence: 100 },
                { text: '生成个性化服务方案', confidence: 98 },
                { text: '规划操作步骤流程', confidence: 95 },
                { text: '准备用户待办清单', confidence: 92 }
            ];
            solutionSteps = this.generateYxtSignupSteps();
            todoList = this.generateUserTodoList();
            serviceTitle = '银信通服务行动方案';
        }

        await this.displayThinkingProcess(thinkingSteps);

        this.hideThinking();

        this.addAIMessage(`
            <div class="bg-gradient-to-r from-blue-900/30 to-green-900/30 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">🎯</span>
                    <span class="font-semibold text-blue-400">${serviceTitle}</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    基于您的需求，我为您制定了完整的操作流程方案：
                </p>

                <div class="mb-6">
                    <h4 class="font-semibold text-green-400 mb-3">📋 操作步骤</h4>
                    <div id="solution-steps-container" class="space-y-3">
                        ${solutionSteps.map((step, index) => `
                            <div class="flex items-start space-x-3 p-3 bg-gray-800/50 rounded-lg">
                                <div class="flex-shrink-0 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-xs font-bold">
                                    ${index + 1}
                                </div>
                                <div class="flex-1">
                                    <div class="font-medium text-gray-200">${step.title}</div>
                                    <div class="text-sm text-gray-400 mt-1">${step.description}</div>
                                    <div class="text-xs text-blue-400 mt-1">预计用时：${step.duration}</div>
                                </div>
                                <div class="text-lg">${step.icon}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-semibold text-yellow-400 mb-3">📝 您需要准备的信息</h4>
                    <div class="space-y-2">
                        ${todoList.map(item => `
                            <div class="flex items-center space-x-2 p-2 bg-gray-800/30 rounded">
                                <span class="text-yellow-400">•</span>
                                <span class="text-sm text-gray-300">${item}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="flex justify-center space-x-3">
                    <button onclick="aiAgent.showSolutionVisualization()"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        📊 可视化方案
                    </button>
                    <button onclick="aiAgent.customizeSolution()"
                            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm">
                        ✏️ 调整方案
                    </button>
                    <button onclick="aiAgent.proceedToExecution()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        ▶️ 开始执行
                    </button>
                </div>
            </div>
        `);
    }

    // 生成银信通签约步骤
    generateYxtSignupSteps() {
        return [
            {
                title: '身份验证',
                description: '完成人脸识别和身份确认，确保账户安全',
                duration: '2分钟',
                icon: '🔐',
                required: true
            },
            {
                title: '银行卡绑定',
                description: '确认并绑定需要开通银信通服务的银行卡',
                duration: '1分钟',
                icon: '💳',
                required: true
            },
            {
                title: '手机号确认',
                description: '验证接收短信的手机号码',
                duration: '1分钟',
                icon: '📱',
                required: true
            },
            {
                title: '短信验证',
                description: '接收并输入短信验证码完成验证',
                duration: '2分钟',
                icon: '💬',
                required: true
            },
            {
                title: '通知设置',
                description: '设置通知起点金额和提醒偏好',
                duration: '3分钟',
                icon: '⚙️',
                required: false
            },
            {
                title: '服务确认',
                description: '确认服务条款并完成银信通签约',
                duration: '1分钟',
                icon: '✅',
                required: true
            }
        ];
    }

    // 生成用户待办清单
    generateUserTodoList() {
        return [
            '准备有效身份证件（用于人脸识别验证）',
            '确认要绑定的银行卡信息',
            '确保手机号码正常接收短信',
            '准备设置通知起点金额（建议100-1000元）',
            '了解银信通服务条款和费用说明'
        ];
    }

    // 生成银信通修改步骤
    generateYxtModifySteps() {
        return [
            {
                title: '身份验证',
                description: '验证身份以确保账户安全',
                duration: '1分钟',
                icon: '🔐',
                required: true
            },
            {
                title: '查看当前设置',
                description: '显示当前银信通服务配置',
                duration: '1分钟',
                icon: '📋',
                required: true
            },
            {
                title: '修改通知设置',
                description: '调整通知起点金额和提醒偏好',
                duration: '3分钟',
                icon: '⚙️',
                required: false
            },
            {
                title: '确认修改',
                description: '确认新的设置并保存',
                duration: '1分钟',
                icon: '✅',
                required: true
            }
        ];
    }

    // 生成银信通解约步骤
    generateYxtCancelSteps() {
        return [
            {
                title: '身份验证',
                description: '验证身份以确保账户安全',
                duration: '1分钟',
                icon: '🔐',
                required: true
            },
            {
                title: '解约确认',
                description: '确认解约银信通服务',
                duration: '2分钟',
                icon: '❌',
                required: true
            },
            {
                title: '费用结算',
                description: '处理剩余费用和退款',
                duration: '1分钟',
                icon: '💰',
                required: true
            },
            {
                title: '服务停止',
                description: '停止银信通短信提醒服务',
                duration: '1分钟',
                icon: '🛑',
                required: true
            }
        ];
    }

    // 生成修改待办清单
    generateModifyTodoList() {
        return [
            '准备身份验证（人脸识别或密码）',
            '确认要修改的设置项目',
            '准备新的通知起点金额（如需修改）',
            '了解修改后的服务条款'
        ];
    }

    // 生成解约待办清单
    generateCancelTodoList() {
        return [
            '准备身份验证（人脸识别或密码）',
            '确认解约原因和影响',
            '了解费用结算方式',
            '确认是否需要备份历史短信记录'
        ];
    }

    // 显示可选推荐（用户主动要求时）
    showOptionalRecommendations(primaryType) {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-blue-400">💡</span>
                    <span class="font-semibold text-blue-400">可选相关推荐</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    以下是与您的核心需求相关的可选服务，您可以根据实际需要选择：
                </p>
                <div id="optional-recommendations" class="space-y-3 mb-4">
                    <!-- 动态生成可选推荐 -->
                </div>
                <div class="text-center">
                    <button onclick="aiAgent.proceedWithOptionalSelections('${primaryType}')"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        继续选择的服务
                    </button>
                </div>
            </div>
        `);

        // 生成可选推荐卡片
        setTimeout(() => {
            this.renderOptionalRecommendations(primaryType);
        }, 500);
    }

    // 渲染可选推荐
    async renderOptionalRecommendations(primaryType) {
        const container = document.getElementById('optional-recommendations');
        if (!container) return;

        const relatedNeeds = this.generateRelatedNeeds(primaryType);

        for (let i = 0; i < relatedNeeds.length; i++) {
            await this.delay(200);
            const need = relatedNeeds[i];

            const cardElement = document.createElement('div');
            cardElement.className = 'optional-recommendation p-3 bg-gray-800/50 rounded-lg border border-gray-700 hover:border-blue-500 transition-all cursor-pointer';
            cardElement.dataset.needId = need.id;

            // 根据业务相关性显示不同的标识
            const relevanceBadge = need.businessRelevance === 'direct'
                ? '<span class="text-xs bg-green-600/20 text-green-400 px-2 py-1 rounded">强关联</span>'
                : '<span class="text-xs bg-blue-600/20 text-blue-400 px-2 py-1 rounded">相关</span>';

            cardElement.innerHTML = `
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3 flex-1">
                        <span class="text-2xl">${need.icon}</span>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <div class="font-medium text-gray-200">${need.title}</div>
                                ${relevanceBadge}
                            </div>
                            <div class="text-sm text-gray-400 mb-2">${need.description}</div>
                            <div class="text-xs text-blue-400">${need.reason}</div>
                        </div>
                    </div>
                    <div class="text-right ml-3">
                        <div class="text-sm font-medium text-blue-400">${need.confidence}%</div>
                        <div class="text-xs text-gray-500">推荐度</div>
                        <div class="mt-2">
                            <input type="checkbox" class="optional-checkbox" data-need-id="${need.id}">
                        </div>
                    </div>
                </div>
            `;

            // 添加点击事件
            cardElement.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    const checkbox = cardElement.querySelector('.optional-checkbox');
                    checkbox.checked = !checkbox.checked;
                }
                this.updateOptionalCardSelection(cardElement, cardElement.querySelector('.optional-checkbox').checked);
            });

            container.appendChild(cardElement);
        }
    }

    updateOptionalCardSelection(cardElement, isSelected) {
        if (isSelected) {
            cardElement.classList.add('border-blue-500', 'bg-blue-900/20');
            cardElement.classList.remove('border-gray-700');
        } else {
            cardElement.classList.remove('border-blue-500', 'bg-blue-900/20');
            cardElement.classList.add('border-gray-700');
        }
    }

    proceedWithOptionalSelections(primaryType) {
        const selectedOptional = [];
        const checkboxes = document.querySelectorAll('.optional-checkbox:checked');

        checkboxes.forEach(checkbox => {
            const needId = checkbox.dataset.needId;
            selectedOptional.push(needId);
        });

        // 包含核心需求和选择的可选服务
        const allSelectedNeeds = [primaryType, ...selectedOptional];

        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm font-medium text-green-400">服务选择完成</span>
                </div>
                <p class="text-sm text-gray-300">
                    已选择 ${allSelectedNeeds.length} 项服务（包含核心需求），正在生成综合解决方案...
                </p>
            </div>
        `);

        // 生成包含所有选择服务的方案
        setTimeout(() => {
            this.generatePersonalizedSolution(allSelectedNeeds.join(','));
        }, 1000);
    }

    generateRelatedNeeds(primaryType) {
        // 优化后的智能预测逻辑 - 聚焦业务生态内的强关联服务
        const needsMap = {
            'sms_notification': [
                {
                    id: 'balance_alert',
                    title: '余额提醒',
                    description: '账户余额低于设定值时提醒',
                    confidence: 95,
                    reason: '银信通核心功能，与账户变动提醒高度关联',
                    icon: '💰',
                    priority: 'high',
                    category: 'sms_ecosystem',
                    businessRelevance: 'direct' // 直接关联
                },
                {
                    id: 'security_alert',
                    title: '安全提醒',
                    description: '异常登录和交易安全提醒',
                    confidence: 92,
                    reason: '银信通安全防护，保障账户资金安全',
                    icon: '🔒',
                    priority: 'high',
                    category: 'sms_ecosystem',
                    businessRelevance: 'direct'
                },
                {
                    id: 'transaction_summary',
                    title: '交易汇总',
                    description: '每日/每周交易汇总短信',
                    confidence: 88,
                    reason: '银信通增值服务，便于账户管理',
                    icon: '📊',
                    priority: 'medium',
                    category: 'sms_ecosystem',
                    businessRelevance: 'direct'
                },
                {
                    id: 'payment_reminder',
                    title: '还款提醒',
                    description: '信用卡和贷款还款提醒',
                    confidence: 82,
                    reason: '银信通提醒服务延伸，避免逾期风险',
                    icon: '📅',
                    priority: 'medium',
                    category: 'sms_ecosystem',
                    businessRelevance: 'indirect' // 间接关联
                }
            ],
            'wealth_management': [
                {
                    id: 'auto_invest',
                    title: '定投服务',
                    description: '定期自动投资理财产品',
                    confidence: 90,
                    reason: '理财生态核心服务，实现资产增值',
                    icon: '📈',
                    priority: 'high',
                    category: 'wealth_ecosystem',
                    businessRelevance: 'direct'
                },
                {
                    id: 'risk_assessment',
                    title: '风险评估',
                    description: '个人投资风险承受能力评估',
                    confidence: 85,
                    reason: '理财前置服务，确保投资安全',
                    icon: '⚖️',
                    priority: 'high',
                    category: 'wealth_ecosystem',
                    businessRelevance: 'direct'
                },
                {
                    id: 'wealth_notification',
                    title: '理财提醒',
                    description: '产品到期、收益变动等提醒',
                    confidence: 78,
                    reason: '理财管理辅助，及时掌握投资动态',
                    icon: '🔔',
                    priority: 'medium',
                    category: 'wealth_ecosystem',
                    businessRelevance: 'indirect'
                }
            ],
            'transfer': [
                {
                    id: 'frequent_payee',
                    title: '常用收款人',
                    description: '保存常用转账对象，快速转账',
                    confidence: 93,
                    reason: '转账核心功能，提升转账效率',
                    icon: '👥',
                    priority: 'high',
                    category: 'transfer_ecosystem',
                    businessRelevance: 'direct'
                },
                {
                    id: 'transfer_limit',
                    title: '转账限额管理',
                    description: '设置日/月转账限额，保障资金安全',
                    confidence: 87,
                    reason: '转账安全保障，防范资金风险',
                    icon: '🛡️',
                    priority: 'high',
                    category: 'transfer_ecosystem',
                    businessRelevance: 'direct'
                }
            ]
        };

        // 只返回与主业务强关联的推荐
        const recommendations = needsMap[primaryType] || [];

        // 优先返回直接关联的服务，然后是间接关联的
        return recommendations.sort((a, b) => {
            if (a.businessRelevance === 'direct' && b.businessRelevance === 'indirect') return -1;
            if (a.businessRelevance === 'indirect' && b.businessRelevance === 'direct') return 1;
            return b.confidence - a.confidence; // 按置信度排序
        });
    }

    async renderPredictedNeedsCards(needs) {
        const container = document.getElementById('predicted-needs');
        if (!container) return;

        for (let i = 0; i < needs.length; i++) {
            await this.delay(300);
            const need = needs[i];

            const cardElement = document.createElement('div');
            cardElement.className = 'predicted-need-card p-3 bg-gray-800/50 rounded-lg border border-gray-700 hover:border-purple-500 transition-all cursor-pointer';
            cardElement.dataset.needId = need.id;

            // 根据业务相关性和优先级显示不同的标识
            const relevanceBadge = need.businessRelevance === 'direct'
                ? '<span class="text-xs bg-green-600/20 text-green-400 px-2 py-1 rounded">强关联</span>'
                : '<span class="text-xs bg-blue-600/20 text-blue-400 px-2 py-1 rounded">相关</span>';

            const priorityBadge = need.priority === 'high'
                ? '<span class="text-xs bg-orange-600/20 text-orange-400 px-2 py-1 rounded ml-1">推荐</span>'
                : '';

            cardElement.innerHTML = `
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3 flex-1">
                        <span class="text-2xl">${need.icon}</span>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <div class="font-medium text-gray-200">${need.title}</div>
                                ${relevanceBadge}${priorityBadge}
                            </div>
                            <div class="text-sm text-gray-400 mb-2">${need.description}</div>
                            <div class="text-xs text-purple-400">${need.reason}</div>
                        </div>
                    </div>
                    <div class="text-right ml-3">
                        <div class="text-sm font-medium text-purple-400">${need.confidence}%</div>
                        <div class="text-xs text-gray-500">推荐度</div>
                        <div class="mt-2">
                            <input type="checkbox" class="need-checkbox" data-need-id="${need.id}"
                                   ${need.priority === 'high' ? 'checked' : ''}>
                        </div>
                    </div>
                </div>
            `;

            // 添加点击事件
            cardElement.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    const checkbox = cardElement.querySelector('.need-checkbox');
                    checkbox.checked = !checkbox.checked;
                }
                this.updateCardSelection(cardElement, cardElement.querySelector('.need-checkbox').checked);
            });

            container.appendChild(cardElement);

            // 高优先级需求默认选中
            if (need.priority === 'high') {
                this.updateCardSelection(cardElement, true);
            }
        }
    }

    updateCardSelection(cardElement, isSelected) {
        if (isSelected) {
            cardElement.classList.add('border-purple-500', 'bg-purple-900/20');
            cardElement.classList.remove('border-gray-700');
        } else {
            cardElement.classList.remove('border-purple-500', 'bg-purple-900/20');
            cardElement.classList.add('border-gray-700');
        }
    }

    confirmPredictedNeeds() {
        const selectedNeeds = [];
        const checkboxes = document.querySelectorAll('.need-checkbox:checked');

        checkboxes.forEach(checkbox => {
            const needId = checkbox.dataset.needId;
            selectedNeeds.push(needId);
        });

        // 生成确认的需求列表
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-green-400">✅</span>
                    <span class="font-semibold text-green-400">需求确认完成</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">
                    您已确认 ${selectedNeeds.length} 项服务需求，我将为您生成个性化解决方案。
                </p>
                <div class="text-center">
                    <button onclick="aiAgent.generatePersonalizedSolution('${selectedNeeds.join(',')}')"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        生成解决方案
                    </button>
                </div>
            </div>
        `);
    }

    // 转账汇款处理
    handleTransferRequest(message) {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-blue-400 mb-3">💸 转账汇款服务</h4>
                <p class="text-sm text-gray-300 mb-4">我可以帮您办理以下转账业务：</p>
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="aiAgent.handleSpecificTransfer('same_bank')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="font-medium text-blue-400">同行转账</div>
                        <div class="text-xs text-gray-400">实时到账，免手续费</div>
                    </button>
                    <button onclick="aiAgent.handleSpecificTransfer('other_bank')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="font-medium text-green-400">跨行转账</div>
                        <div class="text-xs text-gray-400">快速到账，低手续费</div>
                    </button>
                    <button onclick="aiAgent.handleSpecificTransfer('international')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="font-medium text-purple-400">国际汇款</div>
                        <div class="text-xs text-gray-400">全球汇款，安全便捷</div>
                    </button>
                    <button onclick="aiAgent.handleSpecificTransfer('batch')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="font-medium text-yellow-400">批量转账</div>
                        <div class="text-xs text-gray-400">企业专用，高效处理</div>
                    </button>
                </div>
            </div>
        `);
    }

    // 理财投资处理
    handleWealthManagementRequest(message) {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-green-400 mb-3">💰 理财投资服务</h4>
                <p class="text-sm text-gray-300 mb-4">根据您的风险偏好，为您推荐合适的理财产品：</p>
                <div class="space-y-3">
                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium text-green-400">稳健型理财</span>
                            <span class="text-sm text-green-400">年化3.5%</span>
                        </div>
                        <div class="text-xs text-gray-400">低风险，收益稳定，适合保守投资者</div>
                    </div>
                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium text-blue-400">平衡型基金</span>
                            <span class="text-sm text-blue-400">预期5-8%</span>
                        </div>
                        <div class="text-xs text-gray-400">中等风险，平衡收益，适合稳健投资者</div>
                    </div>
                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium text-purple-400">成长型投资</span>
                            <span class="text-sm text-purple-400">预期8-15%</span>
                        </div>
                        <div class="text-xs text-gray-400">较高风险，成长潜力，适合积极投资者</div>
                    </div>
                </div>
                <button onclick="aiAgent.startRiskAssessment()" class="w-full mt-4 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                    开始风险评估
                </button>
            </div>
        `);
    }

    // 贷款申请处理
    handleLoanRequest(message) {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-purple-400 mb-3">🏠 贷款申请服务</h4>
                <p class="text-sm text-gray-300 mb-4">我可以帮您申请以下类型的贷款：</p>
                <div class="space-y-2">
                    <button onclick="aiAgent.handleLoanType('mortgage')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-purple-400">房屋贷款</div>
                                <div class="text-xs text-gray-400">购房首选，利率优惠</div>
                            </div>
                            <div class="text-sm text-purple-400">3.8%起</div>
                        </div>
                    </button>
                    <button onclick="aiAgent.handleLoanType('car')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-blue-400">汽车贷款</div>
                                <div class="text-xs text-gray-400">购车专用，手续简便</div>
                            </div>
                            <div class="text-sm text-blue-400">4.2%起</div>
                        </div>
                    </button>
                    <button onclick="aiAgent.handleLoanType('personal')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-green-400">个人消费贷</div>
                                <div class="text-xs text-gray-400">用途灵活，快速审批</div>
                            </div>
                            <div class="text-sm text-green-400">5.5%起</div>
                        </div>
                    </button>
                </div>
            </div>
        `);
    }

    // 信用卡服务处理
    handleCreditCardRequest(message) {
        this.addAIMessage(`
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-yellow-400 mb-3">💳 信用卡服务</h4>
                <p class="text-sm text-gray-300 mb-4">为您提供全方位的信用卡服务：</p>
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="aiAgent.handleCreditCardService('apply')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-center">
                        <div class="text-2xl mb-1">📝</div>
                        <div class="font-medium text-yellow-400">申请信用卡</div>
                        <div class="text-xs text-gray-400">多种卡片选择</div>
                    </button>
                    <button onclick="aiAgent.handleCreditCardService('limit')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-center">
                        <div class="text-2xl mb-1">📈</div>
                        <div class="font-medium text-blue-400">额度调整</div>
                        <div class="text-xs text-gray-400">提升信用额度</div>
                    </button>
                    <button onclick="aiAgent.handleCreditCardService('installment')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-center">
                        <div class="text-2xl mb-1">🔄</div>
                        <div class="font-medium text-green-400">分期付款</div>
                        <div class="text-xs text-gray-400">灵活还款方式</div>
                    </button>
                    <button onclick="aiAgent.handleCreditCardService('points')" class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-center">
                        <div class="text-2xl mb-1">🎁</div>
                        <div class="font-medium text-purple-400">积分兑换</div>
                        <div class="text-xs text-gray-400">丰富礼品选择</div>
                    </button>
                </div>
            </div>
        `);
    }

    // 显示业务菜单
    showBusinessMenu(message) {
        this.addAIMessage(`
            <div class="bg-gray-800/50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-300 mb-3">🏦 我可以为您提供以下服务</h4>
                <div class="grid grid-cols-2 gap-2 text-sm">
                    <button onclick="aiAgent.handleTransferRequest('转账汇款')" class="p-2 bg-blue-600/20 rounded hover:bg-blue-600/30 transition-colors text-left">
                        <div class="font-medium text-blue-400">💸 转账汇款</div>
                    </button>
                    <button onclick="aiAgent.handleWealthManagementRequest('理财投资')" class="p-2 bg-green-600/20 rounded hover:bg-green-600/30 transition-colors text-left">
                        <div class="font-medium text-green-400">💰 理财投资</div>
                    </button>
                    <button onclick="aiAgent.handleLoanRequest('贷款申请')" class="p-2 bg-purple-600/20 rounded hover:bg-purple-600/30 transition-colors text-left">
                        <div class="font-medium text-purple-400">🏠 贷款申请</div>
                    </button>
                    <button onclick="aiAgent.handleCreditCardRequest('信用卡服务')" class="p-2 bg-yellow-600/20 rounded hover:bg-yellow-600/30 transition-colors text-left">
                        <div class="font-medium text-yellow-400">💳 信用卡</div>
                    </button>
                    <button onclick="aiAgent.handleAccountManagementRequest('账户管理')" class="p-2 bg-cyan-600/20 rounded hover:bg-cyan-600/30 transition-colors text-left">
                        <div class="font-medium text-cyan-400">👤 账户管理</div>
                    </button>
                    <button onclick="aiAgent.handleSmsServiceRequest('sms_notification', '银信通服务')" class="p-2 bg-red-600/20 rounded hover:bg-red-600/30 transition-colors text-left">
                        <div class="font-medium text-red-400">📱 短信通知</div>
                    </button>
                </div>
                <div class="mt-3 text-xs text-gray-400 text-center">
                    请选择您需要的服务，或直接告诉我您的具体需求
                </div>
            </div>
        `);
    }

    toggleVoiceInput() {
        // 语音输入功能（简化版）
        if ('webkitSpeechRecognition' in window) {
            const recognition = new webkitSpeechRecognition();
            recognition.lang = 'zh-CN';
            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                document.getElementById('user-input').value = transcript;
            };
            recognition.start();
        } else {
            alert('您的浏览器不支持语音输入功能');
        }
    }

    initializeTooltips() {
        // 初始化工具提示
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });

            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'fixed bg-gray-900 text-white text-xs px-3 py-2 rounded-lg shadow-lg z-50 pointer-events-none';
        tooltip.textContent = text;
        tooltip.id = 'tooltip';

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

        tooltip.classList.add('animate-fade-in');
    }

    hideTooltip() {
        const tooltip = document.getElementById('tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    startRealtimeUpdates() {
        // 模拟实时数据更新
        setInterval(() => {
            this.updateRealtimeData();
        }, 30000); // 每30秒更新一次
    }

    updateRealtimeData() {
        // 更新活跃度指示器
        const activityBar = document.querySelector('.bg-gradient-to-r');
        if (activityBar) {
            const currentWidth = parseInt(activityBar.style.width) || 85;
            const newWidth = Math.min(95, currentWidth + Math.random() * 5);
            activityBar.style.width = newWidth + '%';
        }

        // 更新AI置信度
        const confidenceBar = document.querySelector('.bg-gradient-to-r.from-blue-500');
        if (confidenceBar) {
            confidenceBar.style.width = (95 + Math.random() * 5) + '%';
        }

        // 更新服务统计
        const statsElements = document.querySelectorAll('.text-lg.font-bold');
        statsElements.forEach(element => {
            if (element.textContent.includes('万')) {
                const current = parseFloat(element.textContent);
                const newValue = (current + Math.random() * 0.1).toFixed(1);
                element.textContent = newValue + '万';
            }
        });
    }

    setupSmartInputValidation() {
        const userInput = document.getElementById('user-input');
        if (!userInput) return;

        let validationTimeout;
        let lastValidation = '';

        userInput.addEventListener('input', (e) => {
            const value = e.target.value.trim();

            // 清除之前的验证定时器
            clearTimeout(validationTimeout);

            // 如果输入为空，清除验证状态
            if (!value) {
                this.clearInputValidation(userInput);
                return;
            }

            // 防抖验证
            validationTimeout = setTimeout(() => {
                if (value !== lastValidation) {
                    this.validateInput(userInput, value);
                    lastValidation = value;
                }
            }, 300);
        });

        // 实时智能提示
        userInput.addEventListener('keyup', (e) => {
            if (e.key !== 'Enter') {
                this.showSmartSuggestions(userInput, e.target.value);
            }
        });
    }

    validateInput(inputElement, value) {
        const validation = this.analyzeInputContent(value);

        // 移除之前的验证状态
        inputElement.classList.remove('border-green-500', 'border-red-500', 'border-yellow-500');

        // 应用新的验证状态
        if (validation.isValid) {
            inputElement.classList.add('border-green-500');
            this.showValidationMessage(validation.message, 'success');
        } else if (validation.hasWarning) {
            inputElement.classList.add('border-yellow-500');
            this.showValidationMessage(validation.message, 'warning');
        } else if (validation.hasError) {
            inputElement.classList.add('border-red-500');
            this.showValidationMessage(validation.message, 'error');
        }
    }

    analyzeInputContent(value) {
        const lowerValue = value.toLowerCase();

        // 检查是否包含敏感信息
        const sensitivePatterns = [
            /\d{4}\s*\d{4}\s*\d{4}\s*\d{4}/, // 银行卡号
            /\d{3}-\d{4}-\d{4}/, // 身份证号部分
            /密码|password|pwd/i, // 密码相关
        ];

        for (let pattern of sensitivePatterns) {
            if (pattern.test(value)) {
                return {
                    isValid: false,
                    hasError: true,
                    message: '⚠️ 请不要输入敏感信息，我们的对话是安全的'
                };
            }
        }

        // 检查意图明确性
        const intentKeywords = [
            '开通', '签约', '办理', '申请',
            '修改', '更改', '调整', '设置',
            '取消', '解约', '关闭', '停用',
            '查询', '查看', '了解', '咨询'
        ];

        const hasIntent = intentKeywords.some(keyword => lowerValue.includes(keyword));

        if (hasIntent) {
            return {
                isValid: true,
                message: '✅ 我理解您的需求，正在准备回复...'
            };
        }

        // 检查是否需要更多信息
        if (value.length < 3) {
            return {
                isValid: false,
                hasWarning: true,
                message: '💡 请详细描述您的需求，我会提供更准确的帮助'
            };
        }

        if (value.length > 200) {
            return {
                isValid: false,
                hasWarning: true,
                message: '💡 消息较长，建议分段描述，我会逐步为您解答'
            };
        }

        return {
            isValid: true,
            message: '✅ 消息格式正确'
        };
    }

    showValidationMessage(message, type) {
        // 移除之前的验证消息
        const existingMessage = document.getElementById('validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建新的验证消息
        const messageElement = document.createElement('div');
        messageElement.id = 'validation-message';
        messageElement.className = `absolute bottom-full left-0 right-0 mb-2 p-2 rounded-lg text-xs transition-all ${
            type === 'success' ? 'bg-green-900/50 text-green-400 border border-green-600/30' :
            type === 'warning' ? 'bg-yellow-900/50 text-yellow-400 border border-yellow-600/30' :
            'bg-red-900/50 text-red-400 border border-red-600/30'
        }`;
        messageElement.textContent = message;

        // 添加到输入框容器
        const inputContainer = document.getElementById('user-input').parentElement;
        inputContainer.style.position = 'relative';
        inputContainer.appendChild(messageElement);

        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                messageElement.remove();
            }, 3000);
        }
    }

    clearInputValidation(inputElement) {
        inputElement.classList.remove('border-green-500', 'border-red-500', 'border-yellow-500');
        const validationMessage = document.getElementById('validation-message');
        if (validationMessage) {
            validationMessage.remove();
        }
    }

    showSmartSuggestions(inputElement, value) {
        if (value.length < 2) {
            this.hideSuggestions();
            return;
        }

        const suggestions = this.generateSmartSuggestions(value);
        if (suggestions.length > 0) {
            this.displaySuggestions(suggestions);
        } else {
            this.hideSuggestions();
        }
    }

    generateSmartSuggestions(value) {
        const lowerValue = value.toLowerCase();
        const suggestions = [];

        // 基于输入内容生成智能建议
        if (lowerValue.includes('转账') || lowerValue.includes('汇款')) {
            suggestions.push(
                '我想转账给朋友',
                '帮我办理跨行转账',
                '国际汇款怎么办理'
            );
        } else if (lowerValue.includes('理财') || lowerValue.includes('投资')) {
            suggestions.push(
                '推荐适合的理财产品',
                '我想了解基金投资',
                '有什么稳健的理财方案'
            );
        } else if (lowerValue.includes('贷款') || lowerValue.includes('借款')) {
            suggestions.push(
                '我想申请房贷',
                '个人消费贷怎么办理',
                '查看贷款利率'
            );
        } else if (lowerValue.includes('信用卡')) {
            suggestions.push(
                '申请信用卡',
                '提升信用卡额度',
                '信用卡分期付款'
            );
        } else if (lowerValue.includes('银信通') || lowerValue.includes('短信') || lowerValue.includes('通知')) {
            suggestions.push(
                '开通银信通服务',
                '修改短信通知设置',
                '取消短信提醒'
            );
        } else if (lowerValue.includes('查询') || lowerValue.includes('余额')) {
            suggestions.push(
                '查询账户余额',
                '下载交易流水',
                '查看账户明细'
            );
        } else {
            // 通用建议
            suggestions.push(
                '我想转账给朋友',
                '推荐理财产品',
                '申请贷款服务',
                '开通短信通知',
                '查询账户余额',
                '办理信用卡'
            );
        }

        // 过滤与当前输入相关的建议
        return suggestions.filter(suggestion =>
            suggestion.toLowerCase().includes(lowerValue) ||
            lowerValue.split('').some(char => suggestion.includes(char))
        ).slice(0, 3);
    }

    displaySuggestions(suggestions) {
        // 移除之前的建议
        this.hideSuggestions();

        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'smart-suggestions';
        suggestionsContainer.className = 'absolute top-full left-0 right-0 mt-2 bg-gray-800 rounded-lg border border-gray-700 shadow-lg z-50';

        suggestions.forEach((suggestion, index) => {
            const suggestionElement = document.createElement('div');
            suggestionElement.className = 'px-4 py-2 hover:bg-gray-700 cursor-pointer text-sm text-gray-300 hover:text-white transition-colors';
            suggestionElement.textContent = suggestion;

            suggestionElement.addEventListener('click', () => {
                document.getElementById('user-input').value = suggestion;
                this.hideSuggestions();
                // 触发验证
                this.validateInput(document.getElementById('user-input'), suggestion);
            });

            suggestionsContainer.appendChild(suggestionElement);
        });

        // 添加到输入框容器
        const inputContainer = document.getElementById('user-input').parentElement;
        inputContainer.style.position = 'relative';
        inputContainer.appendChild(suggestionsContainer);

        // 点击外部隐藏建议
        setTimeout(() => {
            document.addEventListener('click', this.handleOutsideClick.bind(this), { once: true });
        }, 100);
    }

    hideSuggestions() {
        const suggestions = document.getElementById('smart-suggestions');
        if (suggestions) {
            suggestions.remove();
        }
    }

    handleOutsideClick(event) {
        const suggestions = document.getElementById('smart-suggestions');
        const userInput = document.getElementById('user-input');

        if (suggestions && !suggestions.contains(event.target) && event.target !== userInput) {
            this.hideSuggestions();
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter 发送消息
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.sendMessage();
                return;
            }

            // Escape 键清空输入或关闭弹窗
            if (e.key === 'Escape') {
                const userInput = document.getElementById('user-input');
                const suggestions = document.getElementById('smart-suggestions');
                const validationMessage = document.getElementById('validation-message');

                if (suggestions) {
                    this.hideSuggestions();
                } else if (validationMessage) {
                    validationMessage.remove();
                } else if (userInput && userInput.value) {
                    userInput.value = '';
                    this.clearInputValidation(userInput);
                }
                return;
            }

            // Alt + 数字键快速选择方案
            if (e.altKey && e.key >= '1' && e.key <= '3') {
                e.preventDefault();
                const solutionCards = document.querySelectorAll('.solution-card');
                const index = parseInt(e.key) - 1;
                if (solutionCards[index]) {
                    const button = solutionCards[index].querySelector('button[onclick*="selectSolution"]');
                    if (button) button.click();
                }
                return;
            }

            // Ctrl/Cmd + K 聚焦搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const userInput = document.getElementById('user-input');
                if (userInput) {
                    userInput.focus();
                    userInput.select();
                }
                return;
            }

            // F1 显示帮助
            if (e.key === 'F1') {
                e.preventDefault();
                this.showKeyboardShortcuts();
                return;
            }

            // Ctrl/Cmd + D 切换主题
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                this.toggleTheme();
                return;
            }
        });
    }

    showKeyboardShortcuts() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-blue-400 mb-3">⌨️ 键盘快捷键</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">发送消息:</span>
                            <kbd class="px-2 py-1 bg-gray-700 rounded text-xs">Ctrl+Enter</kbd>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">清空输入:</span>
                            <kbd class="px-2 py-1 bg-gray-700 rounded text-xs">Esc</kbd>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">聚焦输入:</span>
                            <kbd class="px-2 py-1 bg-gray-700 rounded text-xs">Ctrl+K</kbd>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">选择方案:</span>
                            <kbd class="px-2 py-1 bg-gray-700 rounded text-xs">Alt+1-3</kbd>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">切换主题:</span>
                            <kbd class="px-2 py-1 bg-gray-700 rounded text-xs">Ctrl+D</kbd>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">显示帮助:</span>
                            <kbd class="px-2 py-1 bg-gray-700 rounded text-xs">F1</kbd>
                        </div>
                    </div>
                </div>
            </div>
        `);
    }

    setupAccessibility() {
        // 添加ARIA标签
        this.addAriaLabels();

        // 键盘导航支持
        this.setupKeyboardNavigation();

        // 屏幕阅读器支持
        this.setupScreenReaderSupport();

        // 高对比度模式检测
        this.setupHighContrastMode();
    }

    addAriaLabels() {
        // 为主要元素添加ARIA标签
        const userInput = document.getElementById('user-input');
        if (userInput) {
            userInput.setAttribute('aria-label', '输入您的问题或需求');
            userInput.setAttribute('aria-describedby', 'input-help');
        }

        // 为按钮添加描述
        document.querySelectorAll('button').forEach((button, index) => {
            if (!button.getAttribute('aria-label')) {
                const text = button.textContent.trim();
                if (text) {
                    button.setAttribute('aria-label', text);
                }
            }
        });

        // 为卡片添加角色
        document.querySelectorAll('.solution-card').forEach((card, index) => {
            card.setAttribute('role', 'option');
            card.setAttribute('aria-label', `方案选项 ${index + 1}`);
            card.setAttribute('tabindex', '0');
        });
    }

    setupKeyboardNavigation() {
        // 为卡片添加键盘导航
        document.querySelectorAll('.solution-card').forEach(card => {
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const button = card.querySelector('button');
                    if (button) button.click();
                }
            });
        });

        // Tab键循环导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.handleTabNavigation(e);
            }
        });
    }

    handleTabNavigation(e) {
        const focusableElements = document.querySelectorAll(
            'button, input, textarea, select, a[href], [tabindex]:not([tabindex="-1"])'
        );

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    setupScreenReaderSupport() {
        // 创建实时区域用于屏幕阅读器
        const liveRegion = document.createElement('div');
        liveRegion.id = 'live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(liveRegion);

        // 监听新消息并通知屏幕阅读器
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE &&
                            (node.classList.contains('ai-message') || node.classList.contains('user-message'))) {
                            const text = node.textContent.trim();
                            if (text) {
                                liveRegion.textContent = `新消息: ${text}`;
                            }
                        }
                    });
                }
            });
        });

        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            observer.observe(chatMessages, { childList: true, subtree: true });
        }
    }

    setupHighContrastMode() {
        // 检测系统高对比度模式
        if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
            document.body.classList.add('high-contrast');
        }

        // 监听对比度偏好变化
        if (window.matchMedia) {
            const contrastQuery = window.matchMedia('(prefers-contrast: high)');
            contrastQuery.addListener((e) => {
                if (e.matches) {
                    document.body.classList.add('high-contrast');
                } else {
                    document.body.classList.remove('high-contrast');
                }
            });
        }
    }

    // 模拟语音输入功能
    toggleVoiceInput() {
        // 模拟语音识别过程，不依赖浏览器API
        this.simulateVoiceRecognition();
    }

    async simulateVoiceRecognition() {
        const voiceButton = document.getElementById('voice-input');
        const userInput = document.getElementById('user-input');

        // 预设的语音转写文本（表达银信通签约的模糊意图）
        const voiceTexts = [
            "我想...开通那个...短信提醒功能",
            "帮我办理账户通知服务",
            "我需要...那个银行短信...怎么开通",
            "想要开通...账户变动提醒",
            "帮我办理...银信通服务吧"
        ];

        const selectedText = voiceTexts[Math.floor(Math.random() * voiceTexts.length)];

        // 开始录音状态
        voiceButton.classList.add('text-red-400', 'animate-pulse');
        userInput.placeholder = '正在听取您的语音...';
        userInput.value = '';

        // 显示正在识别状态
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-blue-400 animate-pulse">🎤</span>
                    <span class="text-blue-400">正在识别您的语音...</span>
                </div>
            </div>
        `);

        // 等待1秒后开始流式转写
        await this.delay(1000);

        // 流式文本转写效果
        await this.streamTextToInput(selectedText, userInput);

        // 结束录音状态
        voiceButton.classList.remove('text-red-400', 'animate-pulse');
        userInput.placeholder = '告诉我您需要什么帮助，比如：我想转账、申请贷款、购买理财产品...';

        // 显示识别完成，精确匹配银信通业务
        this.addAIMessage('✅ 语音识别完成，我理解您想要了解银信通短信通知服务');

        // 等待1秒后直接处理银信通业务（不调用通用业务菜单）
        await this.delay(1000);

        // 直接调用银信通服务处理，确保精确匹配
        this.handleSmsServiceRequest('sms_notification', selectedText);
    }

    async streamTextToInput(text, inputElement) {
        // 流式显示文本，模拟实时语音转文字
        const chars = text.split('');
        inputElement.value = '';

        for (let i = 0; i < chars.length; i++) {
            inputElement.value += chars[i];

            // 模拟语音识别的不均匀速度
            const delay = chars[i] === '.' ? 300 :
                         chars[i] === '...' ? 500 :
                         Math.random() * 100 + 100; // 100-200ms随机间隔

            await this.delay(delay);

            // 实时验证
            this.validateInput(inputElement, inputElement.value);
        }
    }

    toggleCostAnalysis() {
        const costAnalysis = document.getElementById('cost-analysis');
        if (costAnalysis.classList.contains('hidden')) {
            costAnalysis.classList.remove('hidden');
            costAnalysis.classList.add('animate-fade-in-up');
            this.animateCostBars();
        } else {
            this.showDetailedCostAnalysis();
        }
    }

    animateCostBars() {
        const bars = document.querySelectorAll('#cost-analysis .bg-red-400, #cost-analysis .bg-green-400');
        bars.forEach((bar, index) => {
            const targetWidth = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.transition = 'width 1s ease-out';
                bar.style.width = targetWidth;
            }, index * 300);
        });
    }

    showDetailedCostAnalysis() {
        this.addAIMessage(`
            <div class="bg-gradient-to-br from-green-900/20 to-blue-900/20 border border-green-600/30 rounded-xl p-6">
                <h4 class="text-xl font-bold text-green-400 mb-6 flex items-center">
                    <span class="mr-3">📊</span>
                    详细费用分析报告
                </h4>

                <!-- 月度费用对比 -->
                <div class="mb-6">
                    <h5 class="font-semibold text-gray-300 mb-3">月度费用对比</h5>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-red-400 rounded"></div>
                                <span class="text-sm">当前费用</span>
                            </div>
                            <div class="text-right">
                                <div class="font-semibold text-red-400">¥6/月</div>
                                <div class="text-xs text-gray-400">3张卡片</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-green-400 rounded"></div>
                                <span class="text-sm">优化后</span>
                            </div>
                            <div class="text-right">
                                <div class="font-semibold text-green-400">¥2/月</div>
                                <div class="text-xs text-gray-400">1张卡片</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 年度节省预测 -->
                <div class="mb-6">
                    <h5 class="font-semibold text-gray-300 mb-3">年度节省预测</h5>
                    <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-2xl font-bold text-green-400">¥48</div>
                                <div class="text-xs text-gray-400">直接节省</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-blue-400">85%</div>
                                <div class="text-xs text-gray-400">无效通知减少</div>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-purple-400">3分钟</div>
                                <div class="text-xs text-gray-400">每月节省时间</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 使用建议 -->
                <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                    <h5 class="font-semibold text-blue-400 mb-2">💡 AI优化建议</h5>
                    <ul class="text-sm text-gray-300 space-y-1">
                        <li>• 保留工资卡通知服务（高使用频率）</li>
                        <li>• 取消储蓄卡服务（3个月无交易）</li>
                        <li>• 调整通知阈值至50元（减少无效通知）</li>
                        <li>• 建议每季度重新评估使用情况</li>
                    </ul>
                </div>
            </div>
        `);
    }

    setupDemoMode() {
        // 检查URL参数是否包含demo模式
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('demo') === 'true') {
            this.enableDemoMode();
        }

        // 添加演示控制面板
        this.createDemoControls();
    }

    enableDemoMode() {
        this.isDemoMode = true;

        // 显示演示横幅
        const demoBanner = document.createElement('div');
        demoBanner.className = 'fixed top-0 left-0 right-0 bg-yellow-600 text-black text-center py-2 text-sm font-medium z-50';
        demoBanner.innerHTML = '🎭 演示模式 - 这是银信通AI Agent的功能展示';
        document.body.appendChild(demoBanner);

        // 调整主容器位置
        document.getElementById('ai-agent-container').style.paddingTop = '40px';

        // 自动演示流程
        setTimeout(() => {
            this.startAutoDemo();
        }, 3000);
    }

    createDemoControls() {
        const controls = document.createElement('div');
        controls.id = 'demo-controls';
        controls.className = 'fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 border border-gray-700 z-40 hidden';
        controls.innerHTML = `
            <div class="text-xs text-gray-400 mb-2">演示控制</div>
            <div class="flex space-x-2">
                <button onclick="aiAgent.startAutoDemo()" class="px-2 py-1 bg-blue-600 text-white rounded text-xs">
                    自动演示
                </button>
                <button onclick="aiAgent.resetDemo()" class="px-2 py-1 bg-gray-600 text-white rounded text-xs">
                    重置
                </button>
                <button onclick="aiAgent.toggleDemoControls()" class="px-2 py-1 bg-red-600 text-white rounded text-xs">
                    隐藏
                </button>
            </div>
        `;
        document.body.appendChild(controls);

        // 显示控制面板的快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                this.toggleDemoControls();
            }
        });
    }

    toggleDemoControls() {
        const controls = document.getElementById('demo-controls');
        controls.classList.toggle('hidden');
    }

    // 移除自动演示功能，改为用户主导的交互

    resetDemo() {
        // 重置界面状态
        const chatMessages = document.getElementById('chat-messages');
        const welcomeMessage = chatMessages.querySelector('.ai-message');
        chatMessages.innerHTML = '';
        if (welcomeMessage) {
            chatMessages.appendChild(welcomeMessage);
        }

        // 隐藏所有动态内容
        document.getElementById('solution-cards')?.classList.add('hidden');
        document.getElementById('execution-progress')?.classList.add('hidden');
        document.getElementById('ai-thinking')?.classList.add('hidden');

        // 重置状态
        this.state = new AppState();
        this.isAutoDemo = false;
    }

    setupPerformanceMonitoring() {
        // 监控页面加载性能
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('页面加载性能:', {
                loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                totalTime: perfData.loadEventEnd - perfData.fetchStart
            });
        });

        // 监控内存使用
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('内存使用率过高:', memory.usedJSHeapSize / memory.jsHeapSizeLimit);
                }
            }, 30000);
        }

        // 监控长任务
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.duration > 50) {
                        console.warn('检测到长任务:', entry.duration + 'ms');
                    }
                });
            });
            observer.observe({ entryTypes: ['longtask'] });
        }

        // 优化动画性能
        this.optimizeAnimations();
    }

    optimizeAnimations() {
        // 减少动画在低性能设备上的复杂度
        const isLowPerformance = navigator.hardwareConcurrency < 4 ||
                                navigator.deviceMemory < 4;

        if (isLowPerformance) {
            document.body.classList.add('reduced-motion');

            // 简化动画
            const style = document.createElement('style');
            style.textContent = `
                .reduced-motion * {
                    animation-duration: 0.1s !important;
                    transition-duration: 0.1s !important;
                }
            `;
            document.head.appendChild(style);
        }

        // 使用Intersection Observer优化动画
        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                } else {
                    entry.target.classList.remove('animate');
                }
            });
        });

        // 观察需要动画的元素
        document.querySelectorAll('[data-animate]').forEach((el) => {
            animationObserver.observe(el);
        });
    }

    // 账户管理处理
    handleAccountManagementRequest(message) {
        this.addAIMessage(`
            <div class="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-cyan-400 mb-3">👤 账户管理服务</h4>
                <div class="space-y-3">
                    <button onclick="aiAgent.handleAccountService('balance')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">💰</span>
                            <div>
                                <div class="font-medium text-cyan-400">余额查询</div>
                                <div class="text-xs text-gray-400">查看账户余额和明细</div>
                            </div>
                        </div>
                    </button>
                    <button onclick="aiAgent.handleAccountService('statement')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">📊</span>
                            <div>
                                <div class="font-medium text-blue-400">交易流水</div>
                                <div class="text-xs text-gray-400">下载交易记录和对账单</div>
                            </div>
                        </div>
                    </button>
                    <button onclick="aiAgent.handleAccountService('settings')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">⚙️</span>
                            <div>
                                <div class="font-medium text-green-400">账户设置</div>
                                <div class="text-xs text-gray-400">修改密码、绑定手机等</div>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        `);
    }

    // 显示银信通服务选项（精确匹配银信通业务）
    showSmsServiceOptions() {
        this.addAIMessage(`
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-red-400 mb-3">📱 银信通短信服务</h4>
                <p class="text-sm text-gray-300 mb-4">银信通是我行的短信提醒服务，可以在您的账户发生资金变动时及时通知您。请选择您需要的服务：</p>
                <div class="space-y-3">
                    <button onclick="aiAgent.startService('subscribe')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">✅</span>
                            <div>
                                <div class="font-medium text-green-400">开通服务</div>
                                <div class="text-xs text-gray-400">智能开通银信通短信提醒</div>
                            </div>
                        </div>
                    </button>
                    <button onclick="aiAgent.startService('modify')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">⚙️</span>
                            <div>
                                <div class="font-medium text-blue-400">修改设置</div>
                                <div class="text-xs text-gray-400">调整通知阈值和频率</div>
                            </div>
                        </div>
                    </button>
                    <button onclick="aiAgent.startService('cancel')" class="w-full p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors text-left">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">❌</span>
                            <div>
                                <div class="font-medium text-red-400">取消服务</div>
                                <div class="text-xs text-gray-400">智能分析解约建议</div>
                            </div>
                        </div>
                    </button>
                </div>
                <div class="mt-4 text-xs text-gray-400 text-center">
                    💡 基于您的语音需求，为您精准匹配银信通服务
                </div>
            </div>
        `);
    }

    // 占位符方法 - 处理具体业务操作
    handleSpecificTransfer(type) {
        const typeNames = {
            'same_bank': '同行转账',
            'other_bank': '跨行转账',
            'international': '国际汇款',
            'batch': '批量转账'
        };

        this.addAIMessage(`您选择了${typeNames[type]}服务。这是一个演示版本，实际业务请联系银行工作人员办理。`);
    }

    startRiskAssessment() {
        this.addAIMessage(`正在启动风险评估问卷...这是一个演示版本，实际投资请咨询专业理财顾问。`);
    }

    handleLoanType(type) {
        const typeNames = {
            'mortgage': '房屋贷款',
            'car': '汽车贷款',
            'personal': '个人消费贷'
        };

        this.addAIMessage(`您咨询${typeNames[type]}业务。这是一个演示版本，实际贷款申请请前往银行网点办理。`);
    }

    handleCreditCardService(service) {
        const serviceNames = {
            'apply': '申请信用卡',
            'limit': '额度调整',
            'installment': '分期付款',
            'points': '积分兑换'
        };

        this.addAIMessage(`您选择了${serviceNames[service]}服务。这是一个演示版本，实际业务请使用银行官方APP或前往网点办理。`);
    }

    handleAccountService(service) {
        const serviceNames = {
            'balance': '余额查询',
            'statement': '交易流水',
            'settings': '账户设置'
        };

        this.addAIMessage(`您选择了${serviceNames[service]}功能。这是一个演示版本，实际查询请使用银行官方渠道。`);
    }

    handleForexService(service) {
        const serviceNames = {
            'exchange': '外币兑换',
            'remittance': '境外汇款'
        };

        this.addAIMessage(`您选择了${serviceNames[service]}服务。这是一个演示版本，实际外汇业务请前往银行网点办理。`);
    }

    startInsuranceConsultation() {
        this.addAIMessage(`正在为您匹配保险顾问...这是一个演示版本，实际保险咨询请联系银行理财经理。`);
    }

    showFAQ(type) {
        const faqs = {
            'hours': '银行营业时间：周一至周五 9:00-17:00，周末 9:00-16:00',
            'fees': '手续费标准：同行转账免费，跨行转账2-5元/笔',
            'limits': '转账限额：单笔最高50万元，日累计最高100万元',
            'security': '安全提示：请勿向他人透露密码，定期更换登录密码'
        };

        this.addAIMessage(faqs[type] || '暂无相关信息');
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 生成个性化解决方案
    async generatePersonalizedSolution(selectedNeedsStr) {
        const selectedNeeds = selectedNeedsStr.split(',').filter(need => need.trim());

        this.showThinking();

        await this.displayThinkingProcess([
            { text: '整合用户确认的需求', confidence: 100 },
            { text: '分析最优实施路径', confidence: 95 },
            { text: '计算成本效益比', confidence: 92 },
            { text: '生成分步骤行动方案', confidence: 98 },
            { text: '准备可视化展示', confidence: 96 }
        ]);

        this.hideThinking();

        // 动态生成初步方案
        await this.renderDynamicSolution(selectedNeeds);
    }

    async renderDynamicSolution(selectedNeeds) {
        this.addAIMessage(`
            <div class="bg-gradient-to-r from-blue-900/30 to-green-900/30 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">🎯</span>
                    <span class="font-semibold text-blue-400">个性化解决方案</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    基于您确认的需求，我为您制定了以下分步骤行动方案：
                </p>
                <div id="solution-steps" class="space-y-3 mb-4">
                    <!-- 动态生成解决方案步骤 -->
                </div>
                <div class="flex justify-center space-x-3">
                    <button onclick="aiAgent.showSolutionVisualization()"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        📊 查看可视化分析
                    </button>
                    <button onclick="aiAgent.customizeSolution()"
                            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm">
                        ✏️ 自定义方案
                    </button>
                    <button onclick="aiAgent.proceedToExecution()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        ▶️ 开始执行
                    </button>
                </div>
            </div>
        `);

        // 生成解决方案步骤
        await this.delay(500);
        this.generateSolutionSteps(selectedNeeds);
    }

    async generateSolutionSteps(selectedNeeds) {
        const container = document.getElementById('solution-steps');
        if (!container) return;

        const steps = [
            {
                id: 'step1',
                title: '身份验证',
                description: '完成人脸识别和身份确认',
                duration: '2分钟',
                status: 'pending',
                icon: '🔐',
                details: '为了保护您的账户安全，需要进行身份验证'
            },
            {
                id: 'step2',
                title: '服务配置',
                description: '设置银信通提醒偏好',
                duration: '3分钟',
                status: 'pending',
                icon: '⚙️',
                details: '配置提醒类型、时间和接收方式'
            },
            {
                id: 'step3',
                title: '安全设置',
                description: '开启安全提醒功能',
                duration: '2分钟',
                status: 'pending',
                icon: '🛡️',
                details: '设置异常交易和登录提醒'
            },
            {
                id: 'step4',
                title: '测试验证',
                description: '发送测试短信确认',
                duration: '1分钟',
                status: 'pending',
                icon: '✅',
                details: '确保所有设置正常工作'
            }
        ];

        for (let i = 0; i < steps.length; i++) {
            await this.delay(400);
            const step = steps[i];

            const stepElement = document.createElement('div');
            stepElement.className = 'solution-step p-3 bg-gray-800/50 rounded-lg border border-gray-700';
            stepElement.dataset.stepId = step.id;

            stepElement.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="text-2xl">${step.icon}</span>
                        <div>
                            <div class="font-medium text-gray-200">${step.title}</div>
                            <div class="text-sm text-gray-400">${step.description}</div>
                            <div class="text-xs text-blue-400 mt-1">${step.details}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-green-400">${step.duration}</div>
                        <div class="text-xs text-gray-500">预计用时</div>
                        <button onclick="aiAgent.editStep('${step.id}')"
                                class="mt-1 px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs">
                            编辑
                        </button>
                    </div>
                </div>
            `;

            container.appendChild(stepElement);
        }
    }

    // 阶段二：方案可视化与用户确认
    showSolutionVisualization() {
        this.addAIMessage(`
            <div class="bg-indigo-900/20 border border-indigo-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-indigo-400">📊</span>
                    <span class="font-semibold text-indigo-400">银信通签约方案可视化</span>
                </div>

                <!-- 服务价值分析 -->
                <div class="mb-4">
                    <h5 class="text-sm font-medium text-gray-200 mb-2">服务价值分析</h5>
                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-300">银信通月费</span>
                            <span class="text-sm text-blue-400">¥3.00</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-300">账户安全提升</span>
                            <span class="text-sm text-green-400">实时监控</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-300">资金管理便利</span>
                            <span class="text-sm text-green-400">及时掌控</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 95%"></div>
                        </div>
                        <div class="text-xs text-gray-400 mt-1">用户满意度: 95%</div>
                    </div>
                </div>

                <!-- 签约流程时间线 -->
                <div class="mb-4">
                    <h5 class="text-sm font-medium text-gray-200 mb-2">签约流程时间线</h5>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-300">0-2分钟: 身份验证</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span class="text-sm text-gray-300">2-3分钟: 银行卡绑定</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span class="text-sm text-gray-300">3-6分钟: 手机验证</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-300">6-10分钟: 通知设置</span>
                        </div>
                    </div>
                </div>

                <!-- 通知设置预览 -->
                <div class="mb-4">
                    <h5 class="text-sm font-medium text-gray-200 mb-2">通知设置预览</h5>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="p-2 bg-gray-800/50 rounded">
                            <div class="text-xs font-medium text-blue-400">余额变动</div>
                            <div class="text-xs text-gray-400">≥100元时提醒</div>
                        </div>
                        <div class="p-2 bg-gray-800/50 rounded">
                            <div class="text-xs font-medium text-green-400">转账到账</div>
                            <div class="text-xs text-gray-400">实时提醒</div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="aiAgent.confirmSolutionPlan()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        ✅ 确认方案
                    </button>
                </div>
            </div>
        `);
    }

    // 方案自定义功能
    customizeSolution() {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-purple-400">✏️</span>
                    <span class="font-semibold text-purple-400">自定义银信通签约方案</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    您可以调整方案中的步骤，某些必要操作无法修改以确保签约成功：
                </p>

                <div id="customizable-steps" class="space-y-3 mb-4">
                    ${this.generateCustomizableSteps()}
                </div>

                <div class="flex justify-center space-x-3">
                    <button onclick="aiAgent.saveCustomizedSolution()"
                            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm">
                        💾 保存修改
                    </button>
                    <button onclick="aiAgent.resetToDefaultSolution()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm">
                        🔄 恢复默认
                    </button>
                </div>
            </div>
        `);
    }

    // 生成可自定义的步骤
    generateCustomizableSteps() {
        const steps = this.generateYxtSignupSteps();
        return steps.map((step, index) => {
            const isRequired = step.required;
            const editButtonClass = isRequired ? 'bg-gray-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 cursor-pointer';
            const editButtonText = isRequired ? '必需' : '编辑';

            return `
                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <span class="text-lg">${step.icon}</span>
                        <div>
                            <div class="font-medium text-gray-200">${step.title}</div>
                            <div class="text-sm text-gray-400">${step.description}</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-500">${step.duration}</span>
                        <button class="px-2 py-1 ${editButtonClass} rounded text-xs"
                                ${isRequired ? 'disabled' : `onclick="aiAgent.editStep(${index})"`}>
                            ${editButtonText}
                        </button>
                        ${!isRequired ? `
                            <button class="px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs"
                                    onclick="aiAgent.removeStep(${index})">
                                删除
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    simulateScenario(scenario) {
        const scenarios = {
            'high_usage': {
                title: '高频使用场景',
                description: '每月交易20次以上',
                savings: '¥80',
                details: '频繁交易用户通过及时提醒避免透支费用'
            },
            'low_usage': {
                title: '低频使用场景',
                description: '每月交易5次以下',
                savings: '¥20',
                details: '偶尔使用用户主要受益于安全提醒'
            }
        };

        const scenario_data = scenarios[scenario];
        this.addAIMessage(`
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3">
                <h5 class="font-medium text-yellow-400 mb-2">${scenario_data.title}</h5>
                <p class="text-sm text-gray-300 mb-2">${scenario_data.description}</p>
                <div class="text-sm">
                    <span class="text-gray-400">预计月节省: </span>
                    <span class="text-green-400 font-medium">${scenario_data.savings}</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">${scenario_data.details}</div>
            </div>
        `);
    }

    // 阶段三：引导式信息收集
    async proceedToExecution() {
        await this.startGuidedInformationCollection();
    }

    // 开始引导式信息收集流程
    async startGuidedInformationCollection() {
        this.addAIMessage(`
            <div class="bg-gradient-to-r from-green-900/30 to-blue-900/30 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-green-400">🚀</span>
                    <span class="font-semibold text-green-400">开始银信通签约流程</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    现在我将引导您完成银信通签约的每个步骤，请按照提示操作：
                </p>
                <div class="text-center">
                    <button onclick="aiAgent.startIdentityVerification()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        开始身份验证
                    </button>
                </div>
            </div>
        `);
    }

    // 步骤1：身份验证
    async startIdentityVerification() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">🔐</span>
                    <span class="font-semibold text-blue-400">步骤1：身份验证</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    为了保护您的账户安全，需要进行身份验证。请准备好您的身份证件。
                </p>

                <div class="space-y-3 mb-4">
                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="font-medium text-gray-200 mb-2">人脸识别验证</div>
                        <div class="text-sm text-gray-400 mb-3">请确保光线充足，正面面对摄像头</div>
                        <button onclick="aiAgent.simulateFaceRecognition()"
                                class="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                            开始人脸识别
                        </button>
                    </div>

                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="font-medium text-gray-200 mb-2">身份证件验证</div>
                        <div class="text-sm text-gray-400 mb-3">请上传身份证正面照片</div>
                        <button onclick="aiAgent.simulateIdCardUpload()"
                                class="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                            上传身份证
                        </button>
                    </div>
                </div>
            </div>
        `);
    }

    // 模拟人脸识别
    async simulateFaceRecognition() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-blue-400 text-4xl mb-2">📷</div>
                    <div class="text-sm text-gray-300 mb-3">正在进行人脸识别...</div>
                    <div class="w-full bg-gray-700 rounded-full h-2 mb-3">
                        <div id="face-recognition-progress" class="bg-blue-500 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                    </div>
                    <div id="face-recognition-status" class="text-xs text-gray-400">请保持正面面对摄像头</div>
                </div>
            </div>
        `);

        // 模拟进度
        await this.simulateProgress('face-recognition-progress', 'face-recognition-status', [
            '检测人脸...',
            '比对特征点...',
            '验证身份信息...',
            '验证成功！'
        ]);

        setTimeout(() => {
            this.addAIMessage(`
                <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">✅</span>
                        <span class="text-sm text-green-400">人脸识别验证成功</span>
                    </div>
                </div>
            `);
            this.proceedToBankCardBinding();
        }, 3000);
    }

    // 模拟身份证上传
    async simulateIdCardUpload() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-blue-400 text-4xl mb-2">📄</div>
                    <div class="text-sm text-gray-300 mb-3">正在处理身份证信息...</div>
                    <div class="w-full bg-gray-700 rounded-full h-2 mb-3">
                        <div id="id-upload-progress" class="bg-blue-500 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                    </div>
                    <div id="id-upload-status" class="text-xs text-gray-400">正在识别证件信息</div>
                </div>
            </div>
        `);

        await this.simulateProgress('id-upload-progress', 'id-upload-status', [
            '扫描证件图像...',
            '识别文字信息...',
            '验证证件真伪...',
            '信息提取完成！'
        ]);

        setTimeout(() => {
            this.addAIMessage(`
                <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">✅</span>
                        <span class="text-sm text-green-400">身份证验证成功</span>
                    </div>
                </div>
            `);
        }, 3000);
    }

    // 步骤2：银行卡绑定确认
    proceedToBankCardBinding() {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-purple-400">💳</span>
                    <span class="font-semibold text-purple-400">步骤2：确认银行卡信息</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    请确认要开通银信通服务的银行卡信息：
                </p>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm text-gray-300">银行卡号</span>
                        <span class="text-sm text-blue-400">**** **** **** 8888</span>
                    </div>
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm text-gray-300">开户行</span>
                        <span class="text-sm text-blue-400">中国工商银行</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-300">账户类型</span>
                        <span class="text-sm text-blue-400">储蓄卡</span>
                    </div>
                </div>

                <div class="flex justify-center space-x-3">
                    <button onclick="aiAgent.confirmBankCard()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        确认此卡
                    </button>
                    <button onclick="aiAgent.changeBankCard()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm">
                        更换银行卡
                    </button>
                </div>
            </div>
        `);
    }

    // 确认银行卡
    confirmBankCard() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm text-green-400">银行卡信息确认成功</span>
                </div>
            </div>
        `);
        setTimeout(() => {
            this.proceedToPhoneVerification();
        }, 1000);
    }

    // 步骤3：手机号验证
    proceedToPhoneVerification() {
        this.addAIMessage(`
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-yellow-400">📱</span>
                    <span class="font-semibold text-yellow-400">步骤3：手机号验证</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    请确认接收银信通短信的手机号码：
                </p>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm text-gray-300">预留手机号</span>
                        <span class="text-sm text-blue-400">138****8888</span>
                    </div>
                    <div class="text-xs text-gray-400">
                        银信通短信将发送到此手机号码
                    </div>
                </div>

                <div class="flex justify-center space-x-3">
                    <button onclick="aiAgent.sendVerificationCode()"
                            class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-sm">
                        发送验证码
                    </button>
                    <button onclick="aiAgent.changePhoneNumber()"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm">
                        更换手机号
                    </button>
                </div>
            </div>
        `);
    }

    // 发送验证码
    async sendVerificationCode() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-blue-400 text-2xl mb-2">📨</div>
                    <div class="text-sm text-gray-300 mb-3">正在发送验证码到 138****8888</div>
                    <div class="w-full bg-gray-700 rounded-full h-2 mb-3">
                        <div id="sms-send-progress" class="bg-blue-500 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                    </div>
                    <div id="sms-send-status" class="text-xs text-gray-400">正在发送...</div>
                </div>
            </div>
        `);

        await this.simulateProgress('sms-send-progress', 'sms-send-status', [
            '连接短信网关...',
            '发送验证码...',
            '发送成功！'
        ]);

        setTimeout(() => {
            this.showVerificationCodeInput();
        }, 2000);
    }

    // 显示验证码输入界面
    showVerificationCodeInput() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-green-400">💬</span>
                    <span class="font-semibold text-green-400">输入短信验证码</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    验证码已发送到您的手机，请在下方输入6位数字验证码：
                </p>

                <div class="flex justify-center mb-4">
                    <input type="text" id="verification-code" maxlength="6"
                           class="w-32 p-2 bg-gray-800 border border-gray-600 rounded text-center text-lg tracking-widest"
                           placeholder="000000">
                </div>

                <div class="text-center">
                    <button onclick="aiAgent.verifyCode()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        验证
                    </button>
                    <div class="text-xs text-gray-400 mt-2">
                        没收到验证码？<button onclick="aiAgent.resendCode()" class="text-blue-400 hover:underline">重新发送</button>
                    </div>
                </div>
            </div>
        `);
    }

    // 验证码验证
    verifyCode() {
        const code = document.getElementById('verification-code')?.value;
        if (!code || code.length !== 6) {
            this.addAIMessage(`
                <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-red-400">❌</span>
                        <span class="text-sm text-red-400">请输入6位数字验证码</span>
                    </div>
                </div>
            `);
            return;
        }

        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm text-green-400">验证码验证成功</span>
                </div>
            </div>
        `);

        setTimeout(() => {
            this.proceedToNotificationSettings();
        }, 1000);
    }

    // 步骤4：通知设置
    proceedToNotificationSettings() {
        this.addAIMessage(`
            <div class="bg-indigo-900/20 border border-indigo-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-indigo-400">⚙️</span>
                    <span class="font-semibold text-indigo-400">步骤4：通知设置</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    请设置银信通的通知偏好，您可以随时修改这些设置：
                </p>

                <div class="space-y-4 mb-4">
                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <div class="font-medium text-gray-200 mb-3">通知起点金额</div>
                        <div class="text-sm text-gray-400 mb-3">当交易金额达到此数值时发送提醒</div>
                        <div class="flex items-center space-x-3">
                            <span class="text-sm text-gray-300">¥</span>
                            <input type="number" id="notification-amount" value="100" min="1" max="10000"
                                   class="flex-1 p-2 bg-gray-700 border border-gray-600 rounded text-center">
                            <span class="text-sm text-gray-300">元</span>
                        </div>
                        <div class="text-xs text-gray-400 mt-2">建议设置：100-1000元</div>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <div class="font-medium text-gray-200 mb-3">提醒类型</div>
                        <div class="space-y-2">
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" checked class="rounded">
                                <span class="text-sm text-gray-300">余额变动提醒</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" checked class="rounded">
                                <span class="text-sm text-gray-300">转账到账提醒</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="checkbox" checked class="rounded">
                                <span class="text-sm text-gray-300">异常交易提醒</span>
                            </label>
                        </div>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <div class="font-medium text-gray-200 mb-3">提醒时间</div>
                        <div class="grid grid-cols-2 gap-2">
                            <label class="flex items-center space-x-2">
                                <input type="radio" name="notification-time" value="realtime" checked>
                                <span class="text-sm text-gray-300">实时提醒</span>
                            </label>
                            <label class="flex items-center space-x-2">
                                <input type="radio" name="notification-time" value="daily">
                                <span class="text-sm text-gray-300">每日汇总</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="aiAgent.saveNotificationSettings()"
                            class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-sm">
                        保存设置
                    </button>
                </div>
            </div>
        `);
    }

    // 保存通知设置
    saveNotificationSettings() {
        const amount = document.getElementById('notification-amount')?.value || '100';

        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm text-green-400">通知设置保存成功（起点金额：¥${amount}元）</span>
                </div>
            </div>
        `);

        setTimeout(() => {
            this.proceedToFinalConfirmation();
        }, 1000);
    }

    // 最终确认
    proceedToFinalConfirmation() {
        this.addAIMessage(`
            <div class="bg-gradient-to-r from-green-900/30 to-blue-900/30 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-green-400">🎉</span>
                    <span class="font-semibold text-green-400">银信通签约完成确认</span>
                </div>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <h4 class="font-semibold text-gray-200 mb-3">签约信息汇总：</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">服务名称</span>
                            <span class="text-blue-400">银信通短信提醒</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">绑定银行卡</span>
                            <span class="text-blue-400">**** **** **** 8888</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">接收手机号</span>
                            <span class="text-blue-400">138****8888</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">通知起点金额</span>
                            <span class="text-blue-400">¥100元</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">月费用</span>
                            <span class="text-blue-400">¥3.00</span>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3 mb-4">
                    <div class="text-sm text-gray-300">
                        <div class="font-medium text-yellow-400 mb-2">服务条款确认：</div>
                        <div class="space-y-1 text-xs">
                            <div>• 银信通服务费用每月3元，从账户余额中扣除</div>
                            <div>• 服务开通后立即生效，当月开始计费</div>
                            <div>• 您可随时通过网银或手机银行取消服务</div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="aiAgent.completeBankingSignup()"
                            class="px-6 py-3 bg-green-600 hover:bg-green-700 rounded-lg font-medium">
                        确认签约
                    </button>
                </div>
            </div>
        `);
    }

    // 完成银信通签约
    async completeBankingSignup() {
        this.showThinking();

        await this.displayThinkingProcess([
            { text: '提交签约申请', confidence: 100 },
            { text: '系统处理中...', confidence: 98 },
            { text: '开通银信通服务', confidence: 100 },
            { text: '发送确认短信', confidence: 95 }
        ]);

        this.hideThinking();

        this.addAIMessage(`
            <div class="bg-gradient-to-r from-green-900/30 to-emerald-900/30 border border-green-600/30 rounded-lg p-4">
                <div class="text-center">
                    <div class="text-green-400 text-6xl mb-4">🎉</div>
                    <div class="text-xl font-bold text-green-400 mb-2">银信通签约成功！</div>
                    <div class="text-sm text-gray-300 mb-4">
                        恭喜您成功开通银信通短信提醒服务
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                        <div class="text-sm text-gray-300 space-y-2">
                            <div>✅ 服务已激活，即时生效</div>
                            <div>✅ 测试短信已发送到您的手机</div>
                            <div>✅ 通知设置已保存</div>
                            <div>✅ 首月费用将在月底扣除</div>
                        </div>
                    </div>

                    <div class="text-xs text-gray-400 mb-4">
                        您将在5分钟内收到服务开通确认短信
                    </div>

                    <button onclick="aiAgent.showServiceManagement()"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        管理银信通服务
                    </button>
                </div>
            </div>
        `);
    }

    // 进度模拟辅助方法
    async simulateProgress(progressId, statusId, statusTexts) {
        const progressBar = document.getElementById(progressId);
        const statusText = document.getElementById(statusId);

        for (let i = 0; i < statusTexts.length; i++) {
            if (statusText) statusText.textContent = statusTexts[i];
            if (progressBar) {
                progressBar.style.width = `${((i + 1) / statusTexts.length) * 100}%`;
            }
            await this.delay(800);
        }
    }

    // 添加确认方案计划方法
    confirmSolutionPlan() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-green-400">✅</span>
                    <span class="font-semibold text-green-400">方案已确认</span>
                </div>
                <p class="text-sm text-gray-300 mb-4">
                    您已确认银信通签约方案，现在可以开始执行流程。
                </p>
                <div class="text-center">
                    <button onclick="aiAgent.proceedToExecution()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        开始执行
                    </button>
                </div>
            </div>
        `);
    }

    // 添加其他辅助方法
    changeBankCard() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="text-sm text-gray-300">
                    更换银行卡功能正在开发中，请联系客服协助处理。
                </div>
            </div>
        `);
    }

    changePhoneNumber() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="text-sm text-gray-300">
                    更换手机号功能正在开发中，请联系客服协助处理。
                </div>
            </div>
        `);
    }

    resendCode() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="text-sm text-gray-300">
                    验证码已重新发送到您的手机，请注意查收。
                </div>
            </div>
        `);
    }

    // 显示服务管理界面
    showServiceManagement() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">⚙️</span>
                    <span class="font-semibold text-blue-400">银信通服务管理</span>
                </div>

                <div class="space-y-3 mb-4">
                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium text-gray-200">服务状态</span>
                            <span class="text-green-400">已开通</span>
                        </div>
                        <div class="text-sm text-gray-400">银信通短信提醒服务正常运行</div>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium text-gray-200">当前设置</span>
                            <button onclick="aiAgent.handleSmsServiceRequest('yxt_modify', '银信通修改')"
                                    class="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs">
                                修改
                            </button>
                        </div>
                        <div class="text-sm text-gray-400 space-y-1">
                            <div>通知起点金额：¥100元</div>
                            <div>接收手机号：138****8888</div>
                            <div>月费用：¥3.00</div>
                        </div>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium text-gray-200">服务操作</span>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="aiAgent.handleSmsServiceRequest('yxt_modify', '银信通修改')"
                                    class="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 rounded text-sm">
                                修改设置
                            </button>
                            <button onclick="aiAgent.handleSmsServiceRequest('yxt_cancel', '银信通解约')"
                                    class="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm">
                                解约服务
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);
    }

    // 保存自定义方案
    saveCustomizedSolution() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm text-green-400">自定义方案已保存</span>
                </div>
            </div>
        `);
    }

    // 恢复默认方案
    resetToDefaultSolution() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-blue-400">🔄</span>
                    <span class="text-sm text-blue-400">已恢复为默认方案</span>
                </div>
            </div>
        `);
        // 重新显示自定义界面
        setTimeout(() => {
            this.customizeSolution();
        }, 1000);
    }

    // 删除步骤
    removeStep(stepIndex) {
        this.addAIMessage(`
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-yellow-400">🗑️</span>
                    <span class="text-sm text-yellow-400">步骤 ${stepIndex + 1} 已删除</span>
                </div>
            </div>
        `);
    }

    showFinalSolutionSummary() {
        this.addAIMessage(`
            <div class="bg-gradient-to-r from-orange-900/30 to-red-900/30 border border-orange-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-orange-400">📋</span>
                    <span class="font-semibold text-orange-400">最终方案确认</span>
                </div>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <h4 class="font-semibold text-gray-200 mb-3">用大白话说，我将为您做这些事：</h4>
                    <div class="space-y-2 text-sm text-gray-300">
                        <div class="flex items-start space-x-2">
                            <span class="text-blue-400 mt-1">1.</span>
                            <span>开通银信通短信提醒服务，每月收费3元</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <span class="text-blue-400 mt-1">2.</span>
                            <span>设置余额不足提醒，避免透支产生费用</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <span class="text-blue-400 mt-1">3.</span>
                            <span>开启安全提醒，异常交易立即通知您</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <span class="text-blue-400 mt-1">4.</span>
                            <span>发送测试短信，确保一切正常工作</span>
                        </div>
                    </div>
                </div>

                <!-- 风险提示 -->
                <div class="bg-red-900/30 border border-red-600/50 rounded-lg p-3 mb-4">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-red-400">⚠️</span>
                        <span class="font-semibold text-red-400">重要提示</span>
                    </div>
                    <div class="text-sm text-gray-300 space-y-1">
                        <div>• 银信通服务一旦开通，当月即开始计费</div>
                        <div>• 短信费用将从您的账户余额中扣除</div>
                        <div>• 您可以随时通过网银或手机银行取消服务</div>
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="aiAgent.startRiskAssessment()"
                            class="px-6 py-3 bg-orange-600 hover:bg-orange-700 rounded-lg font-medium">
                        我已了解，继续办理
                    </button>
                </div>
            </div>
        `);
    }

    // 动态风险授权
    async startRiskAssessment() {
        this.showThinking();

        await this.displayThinkingProcess([
            { text: '评估交易风险等级', confidence: 100 },
            { text: '分析用户信用状况', confidence: 98 },
            { text: '检查账户安全状态', confidence: 96 },
            { text: '确定授权验证方式', confidence: 99 }
        ]);

        this.hideThinking();

        // 根据风险等级选择授权方式
        const riskLevel = this.calculateRiskLevel();
        this.showDynamicAuthorization(riskLevel);
    }

    calculateRiskLevel() {
        // 模拟风险评估
        const factors = {
            transactionAmount: 3, // 低金额
            userHistory: 'good', // 良好历史
            deviceSecurity: 'high', // 高安全设备
            timeOfDay: 'normal' // 正常时间
        };

        // 银信通开通属于低风险操作
        return 'low';
    }

    showDynamicAuthorization(riskLevel) {
        const authMethods = {
            'low': {
                title: '简单确认',
                description: '低风险操作，仅需确认即可',
                method: 'simple',
                icon: '✅'
            },
            'medium': {
                title: '密码验证',
                description: '中等风险，需要输入交易密码',
                method: 'password',
                icon: '🔑'
            },
            'high': {
                title: '多重验证',
                description: '高风险操作，需要多重身份验证',
                method: 'multi_factor',
                icon: '🔐'
            }
        };

        const auth = authMethods[riskLevel];

        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-green-400">${auth.icon}</span>
                    <span class="font-semibold text-green-400">安全验证 - ${auth.title}</span>
                </div>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-300">风险评估: ${riskLevel.toUpperCase()}</span>
                    </div>
                    <p class="text-sm text-gray-300 mb-3">${auth.description}</p>

                    ${this.renderAuthorizationMethod(auth.method)}
                </div>

                <!-- 不可逆操作强提醒 -->
                <div class="bg-yellow-900/30 border border-yellow-600/50 rounded-lg p-3 mb-4">
                    <div class="flex items-center space-x-2 mb-2">
                        <input type="checkbox" id="risk-confirmation" class="w-4 h-4">
                        <label for="risk-confirmation" class="text-sm text-yellow-400 font-medium">
                            我确认理解此操作的后果
                        </label>
                    </div>
                    <div class="text-xs text-gray-400 ml-6">
                        开通银信通服务后将立即生效并开始计费，请确保您真的需要此服务
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="aiAgent.executeTransaction()"
                            id="execute-btn"
                            class="px-6 py-3 bg-green-600 hover:bg-green-700 rounded-lg font-medium disabled:bg-gray-600 disabled:cursor-not-allowed"
                            disabled>
                        确认执行
                    </button>
                </div>
            </div>
        `);

        // 添加风险确认监听
        setTimeout(() => {
            const checkbox = document.getElementById('risk-confirmation');
            const executeBtn = document.getElementById('execute-btn');

            if (checkbox && executeBtn) {
                checkbox.addEventListener('change', () => {
                    executeBtn.disabled = !checkbox.checked;
                });
            }
        }, 100);
    }

    renderAuthorizationMethod(method) {
        switch (method) {
            case 'simple':
                return `
                    <div class="text-center">
                        <div class="text-sm text-gray-300 mb-2">点击下方按钮即可完成验证</div>
                        <div class="text-xs text-green-400">✓ 系统已验证您的身份</div>
                    </div>
                `;
            case 'password':
                return `
                    <div>
                        <label class="block text-sm text-gray-300 mb-2">请输入交易密码：</label>
                        <input type="password"
                               class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                               placeholder="请输入6位交易密码">
                    </div>
                `;
            case 'multi_factor':
                return `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-2">交易密码：</label>
                            <input type="password"
                                   class="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                                   placeholder="请输入交易密码">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-2">短信验证码：</label>
                            <div class="flex space-x-2">
                                <input type="text"
                                       class="flex-1 p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
                                       placeholder="请输入验证码">
                                <button class="px-4 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                                    获取验证码
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            default:
                return '';
        }
    }

    // 自动化处理与实时可视化
    async executeTransaction() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">⚡</span>
                    <span class="font-semibold text-blue-400">正在执行您的请求</span>
                </div>
                <div id="execution-progress" class="space-y-3">
                    <!-- 动态显示执行进度 -->
                </div>
            </div>
        `);

        // 实时显示执行状态
        await this.showExecutionProgress();
    }

    async showExecutionProgress() {
        const container = document.getElementById('execution-progress');
        if (!container) return;

        const steps = [
            { text: '正在验证用户身份...', duration: 1500, status: 'processing' },
            { text: '正在连接银行核心系统...', duration: 2000, status: 'processing' },
            { text: '正在提交银信通开通申请...', duration: 1800, status: 'processing' },
            { text: '银行系统处理中，请稍候...', duration: 2500, status: 'processing' },
            { text: '正在配置短信提醒服务...', duration: 1200, status: 'processing' },
            { text: '正在发送测试短信...', duration: 1000, status: 'processing' },
            { text: '银信通服务开通成功！', duration: 500, status: 'success' }
        ];

        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];

            // 添加当前步骤
            const stepElement = document.createElement('div');
            stepElement.className = 'flex items-center space-x-3 p-2 bg-gray-800/50 rounded';
            stepElement.innerHTML = `
                <div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span class="text-sm text-gray-300">${step.text}</span>
            `;
            container.appendChild(stepElement);

            // 滚动到当前步骤
            stepElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

            await this.delay(step.duration);

            // 更新步骤状态
            if (step.status === 'success') {
                stepElement.innerHTML = `
                    <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="text-sm text-green-400 font-medium">${step.text}</span>
                `;
            } else {
                stepElement.innerHTML = `
                    <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="text-sm text-gray-400">${step.text}</span>
                `;
            }
        }

        // 执行完成，进入第四阶段
        await this.delay(1000);
        this.proceedToCompletion();
    }

    // 第四阶段：成功反馈与延伸服务
    proceedToCompletion() {
        this.showSuccessAnimation();
    }

    showSuccessAnimation() {
        // 成功反馈动画
        this.addAIMessage(`
            <div class="bg-gradient-to-r from-green-900/30 to-emerald-900/30 border border-green-600/30 rounded-lg p-6 text-center">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3 animate-bounce">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-green-400 mb-2">🎉 办理成功！</h3>
                    <p class="text-gray-300">银信通短信提醒服务已成功开通</p>
                </div>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="text-sm text-gray-300 space-y-1">
                        <div>✅ 服务已激活，立即生效</div>
                        <div>✅ 测试短信已发送到您的手机</div>
                        <div>✅ 月费¥3.00将从账户余额扣除</div>
                        <div class="text-green-400 font-medium mt-2">预计每月为您节省¥50.00</div>
                    </div>
                </div>

                <button onclick="aiAgent.showServiceCertificate()"
                        class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm mb-3">
                    📄 获取办理凭证
                </button>
            </div>
        `);

        // 延迟显示延伸服务
        setTimeout(() => {
            this.showFollowUpServices();
        }, 2000);
    }

    // 凭证获取
    showServiceCertificate() {
        const businessId = this.generateBusinessId();
        const timestamp = new Date().toLocaleString('zh-CN');

        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-blue-400">📄</span>
                    <span class="font-semibold text-blue-400">业务办理凭证</span>
                </div>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div>
                            <span class="text-gray-400">业务类型：</span>
                            <span class="text-gray-200">银信通服务开通</span>
                        </div>
                        <div>
                            <span class="text-gray-400">办理时间：</span>
                            <span class="text-gray-200">${timestamp}</span>
                        </div>
                        <div>
                            <span class="text-gray-400">业务编号：</span>
                            <span class="text-blue-400 font-mono">${businessId}</span>
                        </div>
                        <div>
                            <span class="text-gray-400">办理状态：</span>
                            <span class="text-green-400">成功</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-400">服务内容：</span>
                            <span class="text-gray-200">账户变动短信提醒、余额不足提醒、安全交易提醒</span>
                        </div>
                    </div>
                </div>

                <div class="flex justify-center space-x-3">
                    <button onclick="aiAgent.printCertificate('${businessId}')"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        🖨️ 打印凭证
                    </button>
                    <button onclick="aiAgent.queryCertificateStatus('${businessId}')"
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-sm">
                        🔍 查询状态
                    </button>
                </div>
            </div>
        `);
    }

    generateBusinessId() {
        const timestamp = Date.now().toString();
        const random = Math.random().toString(36).substr(2, 6).toUpperCase();
        return `SMS${timestamp.slice(-8)}${random}`;
    }

    printCertificate(businessId) {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-green-400">🖨️</span>
                    <span class="text-sm font-medium text-green-400">打印准备就绪</span>
                </div>
                <p class="text-sm text-gray-300">
                    业务凭证 ${businessId} 已准备打印，请确保打印机已连接。
                </p>
            </div>
        `);
    }

    queryCertificateStatus(businessId) {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-blue-400">🔍</span>
                    <span class="text-sm font-medium text-blue-400">状态查询结果</span>
                </div>
                <div class="text-sm text-gray-300 space-y-1">
                    <div>业务编号：${businessId}</div>
                    <div>当前状态：<span class="text-green-400">已生效</span></div>
                    <div>生效时间：${new Date().toLocaleString('zh-CN')}</div>
                    <div>下次扣费：${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString('zh-CN')}</div>
                </div>
            </div>
        `);
    }

    // 延伸服务推荐
    showFollowUpServices() {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-purple-400">🚀</span>
                    <span class="font-semibold text-purple-400">为您推荐相关服务</span>
                </div>

                <p class="text-sm text-gray-300 mb-4">
                    基于您刚开通的银信通服务，我发现以下服务可能对您有帮助：
                </p>

                <div class="space-y-3 mb-4">
                    <div class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                         onclick="aiAgent.handleFollowUpService('auto_save')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">💰</span>
                                <div>
                                    <div class="font-medium text-gray-200">自动转存服务</div>
                                    <div class="text-sm text-gray-400">余额超过设定值时自动转入理财</div>
                                    <div class="text-xs text-purple-400">与银信通完美配合</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-green-400">推荐</div>
                                <div class="text-xs text-gray-500">95%匹配</div>
                            </div>
                        </div>
                    </div>

                    <div class="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
                         onclick="aiAgent.handleFollowUpService('payment_assistant')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">📅</span>
                                <div>
                                    <div class="font-medium text-gray-200">还款助手</div>
                                    <div class="text-sm text-gray-400">信用卡、贷款还款提醒和自动还款</div>
                                    <div class="text-xs text-purple-400">避免逾期风险</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-yellow-400">建议</div>
                                <div class="text-xs text-gray-500">78%匹配</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 明确结束出口 -->
                <div class="border-t border-gray-700 pt-4">
                    <div class="flex justify-center space-x-3">
                        <button onclick="aiAgent.completeService()"
                                class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                            ✅ 我已办完，谢谢
                        </button>
                        <button onclick="aiAgent.continueService()"
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                            🔄 继续其他服务
                        </button>
                    </div>
                </div>
            </div>
        `);
    }

    handleFollowUpService(serviceType) {
        const services = {
            'auto_save': {
                name: '自动转存服务',
                description: '智能资金管理，让您的钱生钱'
            },
            'payment_assistant': {
                name: '还款助手',
                description: '再也不用担心忘记还款'
            }
        };

        const service = services[serviceType];
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-blue-400 mb-3">${service.name}</h4>
                <p class="text-sm text-gray-300 mb-4">${service.description}</p>
                <div class="text-center">
                    <button onclick="aiAgent.startNewServiceProcess('${serviceType}')"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        了解详情
                    </button>
                </div>
            </div>
        `);
    }

    // 明确结束出口
    completeService() {
        this.addAIMessage(`
            <div class="bg-gradient-to-r from-green-900/30 to-blue-900/30 border border-green-600/30 rounded-lg p-4 text-center">
                <div class="mb-4">
                    <span class="text-4xl">🙏</span>
                    <h3 class="text-lg font-semibold text-green-400 mt-2">感谢您的信任</h3>
                    <p class="text-gray-300 mt-2">银信通服务已成功开通，祝您使用愉快！</p>
                </div>

                <!-- 轻量化反馈闭环 -->
                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <div class="text-sm text-gray-300 mb-3">请为本次服务体验打分：</div>
                    <div class="flex justify-center space-x-2 mb-3">
                        <button onclick="aiAgent.submitFeedback('excellent')"
                                class="px-3 py-1 bg-green-600/20 hover:bg-green-600/30 rounded-full text-xs text-green-400 border border-green-600/30">
                            😊 非常满意
                        </button>
                        <button onclick="aiAgent.submitFeedback('good')"
                                class="px-3 py-1 bg-blue-600/20 hover:bg-blue-600/30 rounded-full text-xs text-blue-400 border border-blue-600/30">
                            🙂 满意
                        </button>
                        <button onclick="aiAgent.submitFeedback('average')"
                                class="px-3 py-1 bg-yellow-600/20 hover:bg-yellow-600/30 rounded-full text-xs text-yellow-400 border border-yellow-600/30">
                            😐 一般
                        </button>
                        <button onclick="aiAgent.submitFeedback('poor')"
                                class="px-3 py-1 bg-red-600/20 hover:bg-red-600/30 rounded-full text-xs text-red-400 border border-red-600/30">
                            😞 不满意
                        </button>
                    </div>
                </div>

                <div class="text-xs text-gray-400">
                    如需帮助，请随时联系我们的客服团队
                </div>
            </div>
        `);
    }

    continueService() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-blue-400 mb-3">继续为您服务</h4>
                <p class="text-sm text-gray-300 mb-4">请告诉我您还需要什么帮助：</p>
                <div class="grid grid-cols-2 gap-2">
                    <button onclick="aiAgent.handleWealthManagementRequest('理财服务')"
                            class="p-2 bg-green-600/20 rounded hover:bg-green-600/30 transition-colors text-left">
                        <div class="text-sm font-medium text-green-400">💰 理财服务</div>
                    </button>
                    <button onclick="aiAgent.handleTransferRequest('转账汇款')"
                            class="p-2 bg-purple-600/20 rounded hover:bg-purple-600/30 transition-colors text-left">
                        <div class="text-sm font-medium text-purple-400">💸 转账汇款</div>
                    </button>
                    <button onclick="aiAgent.handleLoanRequest('贷款申请')"
                            class="p-2 bg-orange-600/20 rounded hover:bg-orange-600/30 transition-colors text-left">
                        <div class="text-sm font-medium text-orange-400">🏦 贷款申请</div>
                    </button>
                    <button onclick="aiAgent.handleAccountManagementRequest('账户管理')"
                            class="p-2 bg-cyan-600/20 rounded hover:bg-cyan-600/30 transition-colors text-left">
                        <div class="text-sm font-medium text-cyan-400">👤 账户管理</div>
                    </button>
                </div>
            </div>
        `);
    }

    // 轻量化反馈闭环
    submitFeedback(rating) {
        const feedbackMessages = {
            'excellent': '感谢您的五星好评！我们会继续努力提供更好的服务。',
            'good': '感谢您的认可！我们会持续改进服务质量。',
            'average': '感谢您的反馈，我们会努力做得更好。',
            'poor': '很抱歉没有达到您的期望，请告诉我们哪里需要改进。'
        };

        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm font-medium text-green-400">反馈已收到</span>
                </div>
                <p class="text-sm text-gray-300">${feedbackMessages[rating]}</p>
            </div>
        `);

        // 如果是不满意，提供快速原因选择
        if (rating === 'poor') {
            setTimeout(() => {
                this.showFeedbackReasons();
            }, 1000);
        }
    }

    showFeedbackReasons() {
        this.addAIMessage(`
            <div class="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
                <h5 class="font-medium text-red-400 mb-3">请告诉我们具体原因：</h5>
                <div class="space-y-2">
                    <button onclick="aiAgent.submitDetailedFeedback('slow')"
                            class="w-full p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors text-left text-sm">
                        ⏱️ 办理速度太慢
                    </button>
                    <button onclick="aiAgent.submitDetailedFeedback('complex')"
                            class="w-full p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors text-left text-sm">
                        🤔 流程太复杂
                    </button>
                    <button onclick="aiAgent.submitDetailedFeedback('unclear')"
                            class="w-full p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors text-left text-sm">
                        ❓ 说明不够清楚
                    </button>
                    <button onclick="aiAgent.submitDetailedFeedback('other')"
                            class="w-full p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors text-left text-sm">
                        📝 其他原因
                    </button>
                </div>
            </div>
        `);
    }

    submitDetailedFeedback(reason) {
        const reasonMessages = {
            'slow': '我们会优化系统性能，提升办理速度。',
            'complex': '我们会简化流程，让操作更加便捷。',
            'unclear': '我们会改进说明文字，让信息更加清晰。',
            'other': '感谢您的宝贵意见，我们会认真考虑改进。'
        };

        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-3">
                <p class="text-sm text-gray-300">${reasonMessages[reason]}</p>
                <div class="mt-2 text-center">
                    <button onclick="aiAgent.contactHumanAgent()"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm">
                        联系人工客服
                    </button>
                </div>
            </div>
        `);
    }

    // 核心支柱实现

    // 1. "逃生通道"：无缝人工介入
    contactHumanAgent() {
        this.addAIMessage(`
            <div class="bg-gradient-to-r from-orange-900/30 to-red-900/30 border border-orange-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-orange-400">👨‍💼</span>
                    <span class="font-semibold text-orange-400">正在为您接通人工专家</span>
                </div>

                <div class="bg-gray-800/50 rounded-lg p-4 mb-4">
                    <h5 class="font-medium text-gray-200 mb-3">AI自动同步对话历史</h5>
                    <div class="text-sm text-gray-300 space-y-1">
                        <div>✅ 已同步您的身份信息</div>
                        <div>✅ 已同步银信通服务需求</div>
                        <div>✅ 已同步当前办理进度</div>
                        <div>✅ 已同步个性化偏好设置</div>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="text-center">
                        <div class="text-sm text-gray-300 mb-3">请选择联系方式：</div>
                        <div class="grid grid-cols-3 gap-3">
                            <button onclick="aiAgent.initiateVideoCall()"
                                    class="p-3 bg-blue-600/20 rounded-lg hover:bg-blue-600/30 transition-colors">
                                <div class="text-2xl mb-1">📹</div>
                                <div class="text-xs text-blue-400">视频通话</div>
                            </button>
                            <button onclick="aiAgent.initiateVoiceCall()"
                                    class="p-3 bg-green-600/20 rounded-lg hover:bg-green-600/30 transition-colors">
                                <div class="text-2xl mb-1">📞</div>
                                <div class="text-xs text-green-400">语音通话</div>
                            </button>
                            <button onclick="aiAgent.initiateTextChat()"
                                    class="p-3 bg-purple-600/20 rounded-lg hover:bg-purple-600/30 transition-colors">
                                <div class="text-2xl mb-1">💬</div>
                                <div class="text-xs text-purple-400">文字客服</div>
                            </button>
                        </div>
                    </div>

                    <div class="text-center">
                        <div class="text-xs text-gray-400 mb-2">预计等待时间：</div>
                        <div class="text-sm text-green-400">视频: 30秒 | 语音: 15秒 | 文字: 即时</div>
                    </div>
                </div>
            </div>
        `);
    }

    initiateVideoCall() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 text-center">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse">
                        <span class="text-2xl">📹</span>
                    </div>
                    <h4 class="font-semibold text-blue-400">正在连接视频专家</h4>
                    <p class="text-sm text-gray-300 mt-2">专家王经理将在30秒内与您连线</p>
                </div>
                <div class="text-xs text-gray-400">
                    请确保您的摄像头和麦克风已开启
                </div>
            </div>
        `);
    }

    initiateVoiceCall() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4 text-center">
                <div class="mb-4">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse">
                        <span class="text-2xl">📞</span>
                    </div>
                    <h4 class="font-semibold text-green-400">正在连接语音专家</h4>
                    <p class="text-sm text-gray-300 mt-2">专家李经理将在15秒内与您通话</p>
                </div>
                <div class="text-xs text-gray-400">
                    请保持电话畅通
                </div>
            </div>
        `);
    }

    initiateTextChat() {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-purple-400">💬</span>
                    <span class="font-semibold text-purple-400">文字客服已连接</span>
                </div>
                <div class="bg-gray-800/50 rounded-lg p-3 mb-3">
                    <div class="text-sm text-gray-300">
                        <div class="font-medium text-purple-400 mb-1">客服小张：</div>
                        <div>您好！我已收到AI同步的所有信息，了解您要办理银信通服务。我可以立即为您继续办理，或者解答任何疑问。请问您希望我如何帮助您？</div>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <input type="text" placeholder="输入您的问题..."
                           class="flex-1 p-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm">
                    <button class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm">
                        发送
                    </button>
                </div>
            </div>
        `);
    }

    // 2. "信任基石"：绝对透明原则
    showTransparencyInfo() {
        this.addAIMessage(`
            <div class="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="text-cyan-400">🔍</span>
                    <span class="font-semibold text-cyan-400">AI透明度说明</span>
                </div>

                <div class="space-y-3 text-sm text-gray-300">
                    <div>
                        <div class="font-medium text-cyan-400 mb-1">数据使用说明：</div>
                        <div>• 仅使用您的基本账户信息进行个性化推荐</div>
                        <div>• 不会访问您的具体交易金额和收支详情</div>
                        <div>• 所有数据处理均在银行内部系统完成</div>
                    </div>

                    <div>
                        <div class="font-medium text-cyan-400 mb-1">AI决策逻辑：</div>
                        <div>• 基于您的账户活跃度推荐银信通服务</div>
                        <div>• 根据风险评估模型确定验证方式</div>
                        <div>• 使用机器学习优化服务推荐准确性</div>
                    </div>

                    <div>
                        <div class="font-medium text-cyan-400 mb-1">隐私保护：</div>
                        <div>• 对话内容仅用于本次服务，不会永久存储</div>
                        <div>• 个人信息经过加密处理</div>
                        <div>• 严格遵守银行业数据保护规范</div>
                    </div>
                </div>
            </div>
        `);
    }

    // 3. "进化引擎"：持续学习框架
    recordLearningData(eventType, data) {
        // 模拟学习数据记录
        const learningEvent = {
            timestamp: new Date().toISOString(),
            eventType: eventType,
            data: data,
            sessionId: this.sessionId || 'session_' + Date.now()
        };

        // 在实际应用中，这里会发送到学习系统
        console.log('Learning Event Recorded:', learningEvent);

        // 特别关注的学习节点
        if (['user_modified_solution', 'chose_human_agent', 'negative_feedback'].includes(eventType)) {
            this.highlightLearningOpportunity(eventType, data);
        }
    }

    highlightLearningOpportunity(eventType, data) {
        const messages = {
            'user_modified_solution': '用户修改了AI推荐方案，系统将学习用户偏好',
            'chose_human_agent': '用户选择人工服务，系统将分析AI服务的不足',
            'negative_feedback': '用户给出负面反馈，系统将重点优化相关流程'
        };

        // 在开发模式下显示学习提示
        if (window.location.hostname === 'localhost') {
            console.log(`🧠 AI学习机会: ${messages[eventType]}`);
        }
    }

    // 4. "普惠关怀"：无障碍设计
    toggleAccessibilityMode() {
        this.accessibilityMode = !this.accessibilityMode;

        if (this.accessibilityMode) {
            this.enableAccessibilityFeatures();
        } else {
            this.disableAccessibilityFeatures();
        }
    }

    enableAccessibilityFeatures() {
        // 高对比度模式
        document.body.classList.add('high-contrast');

        // 大字体模式
        document.body.classList.add('large-font');

        // 添加无障碍样式
        const style = document.createElement('style');
        style.id = 'accessibility-styles';
        style.textContent = `
            .high-contrast {
                filter: contrast(150%) brightness(110%);
            }
            .large-font {
                font-size: 1.2em !important;
            }
            .large-font * {
                font-size: inherit !important;
            }
            button:focus, input:focus {
                outline: 3px solid #fbbf24 !important;
                outline-offset: 2px !important;
            }
        `;
        document.head.appendChild(style);

        this.addAIMessage(`
            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-yellow-400">♿</span>
                    <span class="font-semibold text-yellow-400">无障碍模式已开启</span>
                </div>
                <div class="text-sm text-gray-300 space-y-2">
                    <div>✅ 高对比度显示</div>
                    <div>✅ 大字体模式</div>
                    <div>✅ 增强焦点指示</div>
                    <div>✅ 屏幕阅读器优化</div>
                </div>
                <div class="mt-3">
                    <button onclick="aiAgent.enableVoiceNavigation()"
                            class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-sm">
                        🎤 开启语音导航
                    </button>
                </div>
            </div>
        `);
    }

    disableAccessibilityFeatures() {
        document.body.classList.remove('high-contrast', 'large-font');
        const style = document.getElementById('accessibility-styles');
        if (style) {
            style.remove();
        }

        this.addAIMessage(`
            <div class="bg-gray-900/20 border border-gray-600/30 rounded-lg p-3">
                <span class="text-gray-400">无障碍模式已关闭</span>
            </div>
        `);
    }

    enableVoiceNavigation() {
        this.addAIMessage(`
            <div class="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-3">
                    <span class="text-blue-400">🎤</span>
                    <span class="font-semibold text-blue-400">语音导航已启用</span>
                </div>
                <div class="text-sm text-gray-300 space-y-2">
                    <div>• 说"下一步"继续操作</div>
                    <div>• 说"重复"重新播放当前信息</div>
                    <div>• 说"帮助"获取语音命令列表</div>
                    <div>• 说"人工客服"直接转接专家</div>
                </div>
                <div class="mt-3 text-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto animate-pulse">
                        <span class="text-white">🎤</span>
                    </div>
                    <div class="text-xs text-blue-400 mt-2">正在监听语音命令...</div>
                </div>
            </div>
        `);
    }

    getPersonalizedRecommendations() {
        return [
            {
                type: 'sms_service',
                priority: 'high',
                reason: '基于交易频率分析',
                confidence: 95
            },
            {
                type: 'wealth_management',
                priority: 'medium',
                reason: '基于资金状况评估',
                confidence: 88
            }
        ];
    }

    getQuickActions() {
        return [
            { id: 'transfer', name: '快速转账', icon: '💸' },
            { id: 'balance', name: '查询余额', icon: '💰' },
            { id: 'history', name: '交易记录', icon: '📊' },
            { id: 'sms_service', name: '银信通', icon: '📱' }
        ];
    }

    getRiskProfile() {
        return {
            level: 'low',
            factors: ['稳定收入', '良好信用', '低风险偏好'],
            recommendations: ['稳健理财', '定期存款', '保险产品']
        };
    }

    handleWealthManagementRequest(service) {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-green-400 mb-3">💰 ${service}</h4>
                <p class="text-sm text-gray-300 mb-4">基于您的风险偏好和资金状况，为您推荐以下理财方案：</p>
                <div class="space-y-3">
                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium text-green-400">稳健型理财</span>
                            <span class="text-sm text-green-400">年化4.2%</span>
                        </div>
                        <p class="text-xs text-gray-400">低风险，适合稳健投资</p>
                    </div>
                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium text-blue-400">定期存款</span>
                            <span class="text-sm text-blue-400">年化3.8%</span>
                        </div>
                        <p class="text-xs text-gray-400">保本保息，安全可靠</p>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button onclick="aiAgent.startWealthManagementProcess()"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                        了解详情
                    </button>
                </div>
            </div>
        `);
    }

    handleTransferRequest(service) {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-purple-400 mb-3">💸 ${service}</h4>
                <p class="text-sm text-gray-300 mb-4">请选择转账方式：</p>
                <div class="grid grid-cols-2 gap-3">
                    <button onclick="aiAgent.startQuickTransfer()"
                            class="p-3 bg-purple-600/20 rounded-lg hover:bg-purple-600/30 transition-colors">
                        <div class="font-medium text-purple-400">快速转账</div>
                        <div class="text-xs text-gray-400">常用收款人</div>
                    </button>
                    <button onclick="aiAgent.startNewTransfer()"
                            class="p-3 bg-purple-600/20 rounded-lg hover:bg-purple-600/30 transition-colors">
                        <div class="font-medium text-purple-400">新建转账</div>
                        <div class="text-xs text-gray-400">输入收款信息</div>
                    </button>
                </div>
            </div>
        `);
    }

    handleAccountManagementRequest(service) {
        this.addAIMessage(`
            <div class="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-cyan-400 mb-3">👤 ${service}</h4>
                <p class="text-sm text-gray-300 mb-4">账户管理服务：</p>
                <div class="space-y-2">
                    <button onclick="aiAgent.showAccountInfo()"
                            class="w-full p-2 bg-cyan-600/20 rounded hover:bg-cyan-600/30 transition-colors text-left">
                        <div class="font-medium text-cyan-400">账户信息</div>
                        <div class="text-xs text-gray-400">查看基本信息和状态</div>
                    </button>
                    <button onclick="aiAgent.showSecuritySettings()"
                            class="w-full p-2 bg-cyan-600/20 rounded hover:bg-cyan-600/30 transition-colors text-left">
                        <div class="font-medium text-cyan-400">安全设置</div>
                        <div class="text-xs text-gray-400">密码、验证方式管理</div>
                    </button>
                    <button onclick="aiAgent.showNotificationSettings()"
                            class="w-full p-2 bg-cyan-600/20 rounded hover:bg-cyan-600/30 transition-colors text-left">
                        <div class="font-medium text-cyan-400">通知设置</div>
                        <div class="text-xs text-gray-400">短信、邮件提醒设置</div>
                    </button>
                </div>
            </div>
        `);
    }

    // 补充缺失的方法
    saveCustomization() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <span class="text-green-400">✅</span>
                    <span class="text-sm font-medium text-green-400">自定义设置已保存</span>
                </div>
                <p class="text-sm text-gray-300 mt-2">您的个性化偏好已更新，将在后续服务中生效。</p>
            </div>
        `);
    }

    editStep(stepId) {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <h5 class="font-medium text-purple-400 mb-3">编辑步骤: ${stepId}</h5>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">步骤名称:</label>
                        <input type="text" class="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                               value="身份验证">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">预计时间:</label>
                        <select class="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white">
                            <option>1分钟</option>
                            <option selected>2分钟</option>
                            <option>3分钟</option>
                            <option>跳过此步骤</option>
                        </select>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="aiAgent.saveStepEdit('${stepId}')"
                                class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded text-sm">
                            保存
                        </button>
                        <button onclick="aiAgent.cancelStepEdit()"
                                class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        `);
    }

    saveStepEdit(stepId) {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-3">
                <span class="text-green-400">✅ 步骤 ${stepId} 已更新</span>
            </div>
        `);

        // 记录用户修改方案的学习数据
        this.recordLearningData('user_modified_solution', { stepId: stepId });
    }

    cancelStepEdit() {
        this.addAIMessage(`
            <div class="bg-gray-900/20 border border-gray-600/30 rounded-lg p-2">
                <span class="text-gray-400">已取消编辑</span>
            </div>
        `);
    }

    startNewServiceProcess(serviceType) {
        this.addAIMessage(`正在为您准备${serviceType}服务，请稍候...`);

        // 重新开始服务流程
        setTimeout(() => {
            this.startNeedsDiscoveryProcess(serviceType, serviceType);
        }, 1000);
    }

    startWealthManagementProcess() {
        this.addAIMessage(`
            <div class="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-green-400 mb-3">💰 理财规划详情</h4>
                <div class="space-y-3">
                    <div class="p-3 bg-gray-800/50 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium text-green-400">智能理财组合</span>
                            <span class="text-sm text-green-400">年化收益 4.5%</span>
                        </div>
                        <div class="text-sm text-gray-300 mb-2">
                            根据您的风险偏好和资金状况，为您配置的多元化投资组合
                        </div>
                        <div class="text-xs text-gray-400">
                            • 60% 稳健型基金 • 30% 债券产品 • 10% 货币基金
                        </div>
                    </div>
                    <div class="text-center">
                        <button onclick="aiAgent.startNeedsDiscoveryProcess('wealth_management', '理财规划')"
                                class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm">
                            开始配置理财方案
                        </button>
                    </div>
                </div>
            </div>
        `);
    }

    startQuickTransfer() {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-purple-400 mb-3">💸 快速转账</h4>
                <div class="space-y-2">
                    <div class="p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors cursor-pointer">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-gray-200">张三</div>
                                <div class="text-xs text-gray-400">工商银行 ****1234</div>
                            </div>
                            <div class="text-sm text-purple-400">常用</div>
                        </div>
                    </div>
                    <div class="p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors cursor-pointer">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-gray-200">李四</div>
                                <div class="text-xs text-gray-400">建设银行 ****5678</div>
                            </div>
                            <div class="text-sm text-purple-400">常用</div>
                        </div>
                    </div>
                </div>
            </div>
        `);
    }

    startNewTransfer() {
        this.addAIMessage(`
            <div class="bg-purple-900/20 border border-purple-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-purple-400 mb-3">💸 新建转账</h4>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">收款人姓名:</label>
                        <input type="text" class="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                               placeholder="请输入收款人姓名">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">收款账号:</label>
                        <input type="text" class="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                               placeholder="请输入收款账号">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">转账金额:</label>
                        <input type="number" class="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white"
                               placeholder="请输入转账金额">
                    </div>
                    <button class="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm">
                        确认转账
                    </button>
                </div>
            </div>
        `);
    }

    showAccountInfo() {
        this.addAIMessage(`
            <div class="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-cyan-400 mb-3">👤 账户信息</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">账户名称:</span>
                        <span class="text-gray-200">张先生</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">账户类型:</span>
                        <span class="text-gray-200">储蓄卡</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">开户时间:</span>
                        <span class="text-gray-200">2020-03-15</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">账户状态:</span>
                        <span class="text-green-400">正常</span>
                    </div>
                </div>
            </div>
        `);
    }

    showSecuritySettings() {
        this.addAIMessage(`
            <div class="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-cyan-400 mb-3">🔒 安全设置</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                        <span class="text-sm text-gray-200">登录密码</span>
                        <button class="px-3 py-1 bg-cyan-600 hover:bg-cyan-700 rounded text-xs">修改</button>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                        <span class="text-sm text-gray-200">交易密码</span>
                        <button class="px-3 py-1 bg-cyan-600 hover:bg-cyan-700 rounded text-xs">修改</button>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                        <span class="text-sm text-gray-200">双重验证</span>
                        <button class="px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-xs">已开启</button>
                    </div>
                </div>
            </div>
        `);
    }

    showNotificationSettings() {
        this.addAIMessage(`
            <div class="bg-cyan-900/20 border border-cyan-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-cyan-400 mb-3">🔔 通知设置</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                        <span class="text-sm text-gray-200">短信通知</span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                        </label>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                        <span class="text-sm text-gray-200">邮件通知</span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                        </label>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-gray-800/50 rounded">
                        <span class="text-sm text-gray-200">推送通知</span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        `);
    }

    handleLoanRequest(service) {
        this.addAIMessage(`
            <div class="bg-orange-900/20 border border-orange-600/30 rounded-lg p-4">
                <h4 class="font-semibold text-orange-400 mb-3">🏦 ${service}</h4>
                <p class="text-sm text-gray-300 mb-4">我们提供多种贷款产品：</p>
                <div class="space-y-2">
                    <div class="p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors cursor-pointer">
                        <div class="font-medium text-orange-400">个人消费贷</div>
                        <div class="text-xs text-gray-400">年利率4.35%起，最高50万</div>
                    </div>
                    <div class="p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors cursor-pointer">
                        <div class="font-medium text-orange-400">房屋抵押贷</div>
                        <div class="text-xs text-gray-400">年利率3.85%起，最高1000万</div>
                    </div>
                    <div class="p-2 bg-gray-800/50 rounded hover:bg-gray-700/50 transition-colors cursor-pointer">
                        <div class="font-medium text-orange-400">信用贷款</div>
                        <div class="text-xs text-gray-400">年利率5.2%起，最高30万</div>
                    </div>
                </div>
            </div>
        `);
    }
}

// 初始化应用
const aiAgent = new BankingAIAgent();

// 全局函数（供HTML调用）
function startService(type) {
    aiAgent.startService(type);
}

function sendMessage() {
    aiAgent.sendMessage();
}
