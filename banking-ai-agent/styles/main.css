/* ===== CSS变量定义 ===== */
:root {
    /* 深色主题色彩 */
    --bg-primary: #030712;
    --bg-secondary: #111827;
    --bg-tertiary: #1f2937;
    --bg-quaternary: #374151;
    
    /* 文字颜色 */
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --text-quaternary: #6b7280;
    
    /* 主题色彩 */
    --accent-blue: #3b82f6;
    --accent-purple: #8b5cf6;
    --accent-green: #10b981;
    --accent-yellow: #f59e0b;
    --accent-red: #ef4444;
    
    /* 边框颜色 */
    --border-primary: #374151;
    --border-secondary: #4b5563;
    --border-accent: #1e40af;
    
    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
    --gradient-success: linear-gradient(135deg, #10b981, #059669);
    --gradient-warning: linear-gradient(135deg, #f59e0b, #d97706);
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* 动画时长 */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* 浅色主题 */
[data-theme="light"] {
    /* 明亮模式背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-quaternary: #e5e7eb;

    /* 明亮模式文字颜色 */
    --text-primary: #111827;
    --text-secondary: #374151;
    --text-tertiary: #6b7280;
    --text-quaternary: #9ca3af;

    /* 明亮模式边框颜色 */
    --border-primary: #e5e7eb;
    --border-secondary: #d1d5db;
    --border-accent: #3b82f6;

    /* 明亮模式阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* 明亮模式渐变 */
    --gradient-primary: linear-gradient(135deg, #3b82f6, #8b5cf6);
    --gradient-success: linear-gradient(135deg, #10b981, #059669);
    --gradient-warning: linear-gradient(135deg, #f59e0b, #d97706);
}

/* ===== 全局样式 ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--duration-normal) ease, color var(--duration-normal) ease;
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--bg-quaternary);
    border-radius: 4px;
    transition: background-color var(--duration-normal) ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-secondary);
}

/* ===== 快速操作按钮 ===== */
.quick-action-btn {
    @apply flex items-center space-x-3 px-4 py-4 bg-gray-800 hover:bg-gray-700 rounded-xl transition-all duration-300 hover:scale-105 border border-gray-700 hover:border-gray-600 relative overflow-hidden;
    background: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-secondary);
    min-height: 80px;
}

.quick-action-btn:hover {
    background: var(--bg-quaternary);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.quick-action-btn:active {
    transform: translateY(0);
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
    left: 100%;
}

/* ===== AI消息样式 ===== */
.ai-message {
    animation: slideInLeft var(--duration-normal) ease-out;
}

.ai-message .bg-gray-900 {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    transition: all var(--duration-normal) ease;
}

.ai-message:hover .bg-gray-900 {
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-md);
}

/* ===== 用户消息样式 ===== */
.user-message {
    animation: slideInRight var(--duration-normal) ease-out;
}

.user-message .bg-blue-900 {
    background: var(--gradient-primary);
    border: 1px solid var(--border-accent);
}

/* ===== 思维链展示动画 ===== */
.thinking-step {
    animation: fadeInUp var(--duration-slow) ease-out;
    @apply flex items-start space-x-2 py-2;
}

.thinking-step:nth-child(1) { animation-delay: 0ms; }
.thinking-step:nth-child(2) { animation-delay: 200ms; }
.thinking-step:nth-child(3) { animation-delay: 400ms; }
.thinking-step:nth-child(4) { animation-delay: 600ms; }

/* ===== 方案卡片 ===== */
.solution-card {
    @apply bg-gray-900 rounded-xl p-6 border-2 border-gray-800 hover:border-blue-600 transition-all cursor-pointer relative overflow-hidden;
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    transition: all var(--duration-normal) ease;
}

.solution-card:hover {
    border-color: var(--accent-blue);
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.solution-card.recommended {
    border-color: var(--accent-blue);
    background: linear-gradient(135deg, var(--bg-secondary), rgba(59, 130, 246, 0.05));
}

.solution-card.recommended::before {
    content: '推荐';
    @apply absolute top-0 right-0 text-white text-xs px-3 py-1 rounded-bl-xl;
    background: var(--gradient-primary);
}

/* ===== 执行步骤 ===== */
.execution-step {
    @apply flex items-center space-x-3 p-3 rounded-lg transition-all;
    transition: all var(--duration-normal) ease;
}

.execution-step.pending {
    background: rgba(75, 85, 99, 0.3);
    color: var(--text-quaternary);
}

.execution-step.active {
    background: rgba(59, 130, 246, 0.1);
    color: var(--accent-blue);
    border: 1px solid rgba(59, 130, 246, 0.3);
    animation: pulse 2s infinite;
}

.execution-step.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-green);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.execution-step .step-icon {
    @apply w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold;
    transition: all var(--duration-normal) ease;
}

.execution-step.pending .step-icon {
    background: var(--bg-quaternary);
    color: var(--text-quaternary);
}

.execution-step.active .step-icon {
    background: var(--accent-blue);
    color: white;
}

.execution-step.completed .step-icon {
    background: var(--accent-green);
    color: white;
}

/* ===== 输入框样式 ===== */
#user-input {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    transition: all var(--duration-normal) ease;
}

#user-input:focus {
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#user-input::placeholder {
    color: var(--text-quaternary);
}

/* ===== 按钮样式 ===== */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    transition: all var(--duration-normal) ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
}

/* ===== 卡片样式 ===== */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    transition: all var(--duration-normal) ease;
}

.card:hover {
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-md);
}

/* ===== 渐变背景 ===== */
.gradient-bg {
    background: var(--gradient-primary);
}

.gradient-success {
    background: var(--gradient-success);
}

.gradient-warning {
    background: var(--gradient-warning);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .lg\\:col-span-2 {
        grid-column: span 1 !important;
    }

    main .grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
    }

    #ai-agent-container {
        padding: 1rem;
    }

    /* 平板优化 */
    .quick-action-btn {
        min-height: 70px;
        padding: 1rem;
    }

    nav .max-w-7xl {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

@media (max-width: 768px) {
    /* 中等屏幕优化 */
    .quick-action-btn {
        flex-direction: row;
        justify-content: flex-start;
        min-height: 60px;
        padding: 0.75rem;
    }

    .quick-action-btn .badge {
        display: none;
    }

    /* 导航栏响应式 */
    nav .flex {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    nav h1 {
        font-size: 1.125rem;
    }

    nav p {
        display: none;
    }
}

@media (max-width: 640px) {
    /* 手机端优化 */
    .quick-action-btn {
        flex-direction: column;
        text-align: center;
        min-height: 80px;
        padding: 1rem 0.75rem;
        gap: 0.5rem;
    }

    .quick-action-btn > div:first-child {
        margin: 0 auto;
    }

    .quick-action-btn .text-left {
        text-align: center;
    }

    .grid-cols-3 {
        grid-template-columns: 1fr !important;
        gap: 0.75rem;
    }

    .sm\\:grid-cols-3 {
        grid-template-columns: 1fr !important;
    }

    /* 移动端间距调整 */
    .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-8 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
    }

    /* 卡片间距优化 */
    .space-y-4 > * + * {
        margin-top: 1rem;
    }

    /* 消息区域优化 */
    #chat-messages {
        min-height: 300px;
    }

    /* 输入区域优化 */
    .bottom-0 .flex {
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    #user-input {
        min-width: 0;
        flex: 1 1 100%;
    }

    .bottom-0 button {
        flex: 1 1 auto;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    /* 超小屏幕优化 */
    .rounded-2xl {
        border-radius: 1rem;
    }

    .p-6 {
        padding: 1rem;
    }

    .p-4 {
        padding: 0.75rem;
    }

    /* 字体大小调整 */
    .text-lg {
        font-size: 1rem;
    }

    .text-xl {
        font-size: 1.125rem;
    }

    /* 导航栏紧凑模式 */
    nav .py-4 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }

    nav button span {
        display: none;
    }

    nav button {
        padding: 0.5rem;
    }
}

/* ===== 主题切换动画 ===== */
* {
    transition: background-color var(--duration-normal) ease, 
                color var(--duration-normal) ease, 
                border-color var(--duration-normal) ease;
}

/* ===== 加载状态 ===== */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 1.5s infinite;
}

/* ===== 工具提示 ===== */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--duration-normal) ease;
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
}

/* ===== 状态指示器 ===== */
.status-indicator {
    @apply w-2 h-2 rounded-full;
}

.status-indicator.online {
    background: var(--accent-green);
    animation: pulse 2s infinite;
}

.status-indicator.offline {
    background: var(--text-quaternary);
}

.status-indicator.busy {
    background: var(--accent-yellow);
    animation: pulse 2s infinite;
}

/* ===== 徽章样式 ===== */
.badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.badge.primary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--accent-blue);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.badge.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-green);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--accent-yellow);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--accent-red);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* ===== 明亮模式专用样式 ===== */
[data-theme="light"] body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* 明亮模式导航栏 */
[data-theme="light"] nav {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
}

[data-theme="light"] nav h1 {
    color: var(--text-primary);
}

[data-theme="light"] nav p {
    color: var(--text-secondary);
}

/* 明亮模式主容器 */
[data-theme="light"] main {
    background-color: var(--bg-secondary);
}

/* 明亮模式聊天区域 */
[data-theme="light"] #chat-messages {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
}

/* 明亮模式AI消息 */
[data-theme="light"] .ai-message .bg-gray-900 {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] .ai-message .text-gray-300,
[data-theme="light"] .ai-message .text-gray-200,
[data-theme="light"] .ai-message .text-white {
    color: var(--text-primary) !important;
}

[data-theme="light"] .ai-message .text-gray-400 {
    color: var(--text-tertiary) !important;
}

[data-theme="light"] .ai-message .text-gray-500 {
    color: var(--text-quaternary) !important;
}

/* 明亮模式用户消息 */
[data-theme="light"] .user-message .bg-blue-900 {
    background: var(--gradient-primary) !important;
    color: white !important;
}

/* 明亮模式思维过程 */
[data-theme="light"] .thinking-step {
    color: var(--text-secondary);
}

[data-theme="light"] .thinking-step .text-gray-400 {
    color: var(--text-tertiary) !important;
}

/* 明亮模式卡片 */
[data-theme="light"] .card,
[data-theme="light"] .solution-card {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] .card:hover,
[data-theme="light"] .solution-card:hover {
    border-color: var(--border-secondary) !important;
    box-shadow: var(--shadow-md) !important;
}

/* 明亮模式快速操作按钮 */
[data-theme="light"] .quick-action-btn {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] .quick-action-btn:hover {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-secondary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] .quick-action-btn .text-gray-300,
[data-theme="light"] .quick-action-btn .text-gray-200 {
    color: var(--text-primary) !important;
}

[data-theme="light"] .quick-action-btn .text-gray-400 {
    color: var(--text-tertiary) !important;
}

/* 明亮模式输入框 */
[data-theme="light"] #user-input {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] #user-input::placeholder {
    color: var(--text-quaternary) !important;
}

[data-theme="light"] #user-input:focus {
    border-color: var(--accent-blue) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* 明亮模式按钮 */
[data-theme="light"] .btn-primary {
    background: var(--gradient-primary) !important;
    color: white !important;
}

[data-theme="light"] button {
    color: var(--text-primary);
}

[data-theme="light"] button.bg-gray-600 {
    background-color: var(--bg-quaternary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] button.bg-gray-600:hover {
    background-color: var(--text-quaternary) !important;
}

/* 明亮模式右侧面板 */
[data-theme="light"] .bg-gray-900 {
    background-color: var(--bg-primary) !important;
    border: 1px solid var(--border-primary) !important;
}

[data-theme="light"] .bg-gray-800 {
    background-color: var(--bg-secondary) !important;
    border: 1px solid var(--border-primary) !important;
}

[data-theme="light"] .bg-gray-700 {
    background-color: var(--bg-tertiary) !important;
}

/* 明亮模式文字颜色统一 */
[data-theme="light"] .text-white {
    color: var(--text-primary) !important;
}

[data-theme="light"] .text-gray-100 {
    color: var(--text-primary) !important;
}

[data-theme="light"] .text-gray-200 {
    color: var(--text-primary) !important;
}

[data-theme="light"] .text-gray-300 {
    color: var(--text-secondary) !important;
}

[data-theme="light"] .text-gray-400 {
    color: var(--text-tertiary) !important;
}

[data-theme="light"] .text-gray-500 {
    color: var(--text-quaternary) !important;
}

/* 明亮模式边框颜色 */
[data-theme="light"] .border-gray-700 {
    border-color: var(--border-primary) !important;
}

[data-theme="light"] .border-gray-600 {
    border-color: var(--border-secondary) !important;
}

[data-theme="light"] .border-gray-800 {
    border-color: var(--border-primary) !important;
}

/* 明亮模式特殊组件 */
[data-theme="light"] .bg-purple-900 {
    background-color: rgba(139, 92, 246, 0.1) !important;
    border-color: rgba(139, 92, 246, 0.2) !important;
}

[data-theme="light"] .bg-green-900 {
    background-color: rgba(16, 185, 129, 0.1) !important;
    border-color: rgba(16, 185, 129, 0.2) !important;
}

[data-theme="light"] .bg-blue-900 {
    background-color: rgba(59, 130, 246, 0.1) !important;
    border-color: rgba(59, 130, 246, 0.2) !important;
}

[data-theme="light"] .bg-red-900 {
    background-color: rgba(239, 68, 68, 0.1) !important;
    border-color: rgba(239, 68, 68, 0.2) !important;
}

[data-theme="light"] .bg-yellow-900 {
    background-color: rgba(245, 158, 11, 0.1) !important;
    border-color: rgba(245, 158, 11, 0.2) !important;
}

/* 明亮模式进度条和状态指示器 */
[data-theme="light"] .progress-bar {
    background-color: var(--bg-tertiary) !important;
}

[data-theme="light"] .progress-fill {
    background: var(--gradient-primary) !important;
}

/* 明亮模式滚动条 */
[data-theme="light"] ::-webkit-scrollbar-track {
    background: var(--bg-tertiary) !important;
}

[data-theme="light"] ::-webkit-scrollbar-thumb {
    background: var(--border-secondary) !important;
}

[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
    background: var(--text-quaternary) !important;
}

/* 明亮模式工具提示 */
[data-theme="light"] .tooltip::before {
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-primary) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* 明亮模式徽章 */
[data-theme="light"] .badge.primary {
    background: rgba(59, 130, 246, 0.1) !important;
    color: var(--accent-blue) !important;
    border-color: rgba(59, 130, 246, 0.2) !important;
}

[data-theme="light"] .badge.success {
    background: rgba(16, 185, 129, 0.1) !important;
    color: var(--accent-green) !important;
    border-color: rgba(16, 185, 129, 0.2) !important;
}

[data-theme="light"] .badge.warning {
    background: rgba(245, 158, 11, 0.1) !important;
    color: var(--accent-yellow) !important;
    border-color: rgba(245, 158, 11, 0.2) !important;
}

[data-theme="light"] .badge.error {
    background: rgba(239, 68, 68, 0.1) !important;
    color: var(--accent-red) !important;
    border-color: rgba(239, 68, 68, 0.2) !important;
}

/* 明亮模式执行步骤 */
[data-theme="light"] .execution-step.pending {
    background: rgba(156, 163, 175, 0.1) !important;
    color: var(--text-quaternary) !important;
}

[data-theme="light"] .execution-step.active {
    background: rgba(59, 130, 246, 0.1) !important;
    color: var(--accent-blue) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
}

[data-theme="light"] .execution-step.completed {
    background: rgba(16, 185, 129, 0.1) !important;
    color: var(--accent-green) !important;
    border-color: rgba(16, 185, 129, 0.3) !important;
}

/* 明亮模式预测需求卡片 */
[data-theme="light"] .predicted-need-card {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] .predicted-need-card:hover {
    border-color: var(--accent-purple) !important;
    box-shadow: var(--shadow-md) !important;
}

[data-theme="light"] .optional-recommendation {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] .optional-recommendation:hover {
    border-color: var(--accent-blue) !important;
}

/* 明亮模式选中状态 */
[data-theme="light"] .predicted-need-card.selected,
[data-theme="light"] .optional-recommendation.selected {
    background-color: rgba(59, 130, 246, 0.05) !important;
    border-color: var(--accent-blue) !important;
}

/* 明亮模式加载状态 */
[data-theme="light"] .loading::after {
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent) !important;
}

/* 明亮模式阴影效果 */
[data-theme="light"] .shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

[data-theme="light"] .shadow-md {
    box-shadow: var(--shadow-md) !important;
}

[data-theme="light"] .shadow-sm {
    box-shadow: var(--shadow-sm) !important;
}

[data-theme="light"] .shadow-xl {
    box-shadow: var(--shadow-xl) !important;
}

/* 明亮模式全局过渡样式 */
[data-theme="light"] * {
    transition: background-color var(--duration-normal) ease,
                color var(--duration-normal) ease,
                border-color var(--duration-normal) ease;
}

/* 明亮模式动态生成元素支持 */
[data-theme="light"] [id*="chat-messages"] * {
    color: inherit;
    background-color: inherit;
}

[data-theme="light"] .ai-message *:not([style*="color"]) {
    color: var(--text-primary);
}

[data-theme="light"] .user-message *:not([style*="color"]) {
    color: inherit;
}

/* 明亮模式文字颜色 - 允许内联样式覆盖 */
[data-theme="light"] .text-white:not([style*="color"]),
[data-theme="light"] .text-gray-100:not([style*="color"]),
[data-theme="light"] .text-gray-200:not([style*="color"]) {
    color: var(--text-primary);
}

[data-theme="light"] .text-gray-300:not([style*="color"]) {
    color: var(--text-secondary);
}

[data-theme="light"] .text-gray-400:not([style*="color"]) {
    color: var(--text-tertiary);
}

[data-theme="light"] .text-gray-500:not([style*="color"]),
[data-theme="light"] .text-gray-600:not([style*="color"]) {
    color: var(--text-quaternary);
}

/* 明亮模式背景颜色 - 允许内联样式覆盖 */
[data-theme="light"] .bg-gray-900:not([style*="background"]),
[data-theme="light"] .bg-gray-800:not([style*="background"]) {
    background-color: var(--bg-primary);
}

[data-theme="light"] .bg-gray-700:not([style*="background"]) {
    background-color: var(--bg-secondary);
}

[data-theme="light"] .bg-gray-600:not([style*="background"]) {
    background-color: var(--bg-tertiary);
}

[data-theme="light"] .bg-gray-500:not([style*="background"]) {
    background-color: var(--bg-quaternary);
}

/* 明亮模式强制边框颜色 */
[data-theme="light"] .border-gray-800,
[data-theme="light"] .border-gray-700 {
    border-color: var(--border-primary) !important;
}

[data-theme="light"] .border-gray-600 {
    border-color: var(--border-secondary) !important;
}

/* 明亮模式特殊背景处理 */
[data-theme="light"] .bg-gray-950 {
    background-color: var(--bg-secondary) !important;
}

[data-theme="light"] .bg-gray-900\/50 {
    background-color: var(--bg-primary) !important;
}

[data-theme="light"] .bg-gray-800\/50 {
    background-color: var(--bg-secondary) !important;
}

/* 明亮模式动态内容样式修复 */
[data-theme="light"] #chat-messages .ai-message {
    color: var(--text-primary);
}

[data-theme="light"] #chat-messages .user-message {
    color: inherit;
}

[data-theme="light"] #personalized-greeting {
    color: var(--text-primary) !important;
}

[data-theme="light"] #ai-thinking {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="light"] #execution-progress {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

/* 明亮模式按钮和交互元素 */
[data-theme="light"] button:not(.bg-red-600):not(.bg-green-600):not(.bg-blue-600):not(.bg-purple-600):not(.bg-yellow-600):not([style*="background"]) {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

[data-theme="light"] button:not(.bg-red-600):not(.bg-green-600):not(.bg-blue-600):not(.bg-purple-600):not(.bg-yellow-600):not([style*="background"]):hover {
    background-color: var(--bg-quaternary);
    border-color: var(--border-secondary);
}

/* 明亮模式输入框和表单元素 */
[data-theme="light"] input,
[data-theme="light"] textarea,
[data-theme="light"] select {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-primary) !important;
}

[data-theme="light"] input::placeholder,
[data-theme="light"] textarea::placeholder {
    color: var(--text-quaternary) !important;
}

[data-theme="light"] input:focus,
[data-theme="light"] textarea:focus,
[data-theme="light"] select:focus {
    border-color: var(--accent-blue) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* 明亮模式链接和可点击元素 */
[data-theme="light"] a {
    color: var(--accent-blue) !important;
}

[data-theme="light"] a:hover {
    color: var(--accent-purple) !important;
}

/* 明亮模式图标颜色 */
[data-theme="light"] svg {
    color: inherit !important;
}

/* 明亮模式特殊状态 */
[data-theme="light"] .hover\\:bg-gray-700:hover {
    background-color: var(--bg-tertiary) !important;
}

[data-theme="light"] .hover\\:bg-gray-600:hover {
    background-color: var(--bg-quaternary) !important;
}

[data-theme="light"] .hover\\:border-gray-600:hover {
    border-color: var(--border-secondary) !important;
}

[data-theme="light"] .hover\\:border-gray-700:hover {
    border-color: var(--border-primary) !important;
}

/* 明亮模式渐变背景保持 */
[data-theme="light"] .bg-gradient-to-r,
[data-theme="light"] .bg-gradient-to-br,
[data-theme="light"] .bg-gradient-to-l,
[data-theme="light"] .bg-gradient-to-t,
[data-theme="light"] .bg-gradient-to-b {
    /* 保持渐变背景不变 */
    background: inherit !important;
}

/* 明亮模式确保可读性 */
[data-theme="light"] {
    --text-contrast-ratio: 4.5; /* WCAG AA标准 */
}

/* 明亮模式高对比度文字 */
[data-theme="light"] .high-contrast {
    color: #000000 !important;
    font-weight: 600 !important;
}

/* 明亮模式无障碍优化 */
[data-theme="light"] :focus {
    outline: 2px solid var(--accent-blue) !important;
    outline-offset: 2px !important;
}
